#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

"""
Core functionality tests for Floating IP Blocking.
Combines plugin tests, database tests, and rule validation tests.
"""

import mock
from unittest import mock as umock

from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import constants
from neutron_lib import context
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from oslo_utils import uuidutils

from neutron.objects import floating_ip_blocking as fip_blocking_obj
from neutron.objects import router as router_obj
from neutron.services.floating_ip_blocking.common import exceptions as fip_blocking_exc
from neutron.services.floating_ip_blocking import plugin as fip_blocking_plugin
from neutron.services.floating_ip_blocking.rule_validator import RuleValidator
from neutron.services.floating_ip_blocking.rule_validator import ConflictDetector
from neutron.tests import base
from neutron.tests.unit import testlib_api


class FloatingIPBlockingTestBase(base.BaseTestCase):
    """Base test class with common setup and utilities."""

    def setUp(self):
        super(FloatingIPBlockingTestBase, self).setUp()
        self.context = context.get_admin_context()
        self.plugin = fip_blocking_plugin.FloatingIPBlockingPlugin()
        
        # Mock dependencies
        self.l3_plugin_mock = mock.Mock()
        self.core_plugin_mock = mock.Mock()
        
        with mock.patch.object(directory, 'get_plugin') as get_plugin_mock:
            def side_effect(plugin_type):
                if plugin_type == plugin_constants.L3:
                    return self.l3_plugin_mock
                return self.core_plugin_mock
            get_plugin_mock.side_effect = side_effect
            
        # Test data
        self.floatingip_id = uuidutils.generate_uuid()
        self.project_id = uuidutils.generate_uuid()
        self.blocking_table_id = uuidutils.generate_uuid()
        self.rule_id = uuidutils.generate_uuid()

    def _create_mock_floatingip(self):
        """Create mock floating IP object."""
        fip = mock.Mock()
        fip.id = self.floatingip_id
        fip.project_id = self.project_id
        fip.floating_ip_address = '*************'
        return fip

    def _create_mock_blocking_table(self):
        """Create mock blocking table object."""
        table = mock.Mock()
        table.id = self.blocking_table_id
        table.floatingip_id = self.floatingip_id
        table.enabled = True
        table.project_id = self.project_id
        return table

    def _create_mock_blocking_rule(self, **kwargs):
        """Create mock blocking rule object."""
        rule = mock.Mock()
        rule.id = kwargs.get('id', self.rule_id)
        rule.blocking_table_id = kwargs.get('blocking_table_id', self.blocking_table_id)
        rule.protocol = kwargs.get('protocol', 'tcp')
        rule.local_port = kwargs.get('local_port', 80)
        rule.remote_ip = kwargs.get('remote_ip', '10.0.0.0/24')
        rule.remote_port = kwargs.get('remote_port', None)
        rule.direction = kwargs.get('direction', 'ingress')
        rule.description = kwargs.get('description', 'Test rule')
        return rule


# Plugin Core Functionality Tests
class TestFloatingIPBlockingPlugin(FloatingIPBlockingTestBase):
    """Test core plugin functionality."""

    @mock.patch.object(router_obj.FloatingIP, 'get_object')
    def test_validate_floatingip_exists_success(self, mock_get_object):
        """Test floating IP validation - success case."""
        mock_fip = self._create_mock_floatingip()
        mock_get_object.return_value = mock_fip

        result = self.plugin._validate_floatingip_exists(self.context, self.floatingip_id)
        
        self.assertEqual(result, mock_fip)
        mock_get_object.assert_called_once_with(self.context, id=self.floatingip_id)

    @mock.patch.object(router_obj.FloatingIP, 'get_object')
    def test_validate_floatingip_exists_not_found(self, mock_get_object):
        """Test floating IP validation - not found case."""
        mock_get_object.return_value = None

        self.assertRaises(
            fip_blocking_exc.FloatingIPNotFound,
            self.plugin._validate_floatingip_exists,
            self.context, self.floatingip_id
        )

    @mock.patch.object(fip_blocking_obj.FloatingIPBlockingTable, 'get_object')
    def test_get_blocking_table_exists(self, mock_get_object):
        """Test getting existing blocking table."""
        mock_table = self._create_mock_blocking_table()
        mock_get_object.return_value = mock_table

        result = self.plugin._get_blocking_table(self.context, self.floatingip_id)
        
        self.assertEqual(result, mock_table)

    @mock.patch.object(fip_blocking_obj.FloatingIPBlockingRule, 'get_objects')
    @mock.patch.object(fip_blocking_obj.FloatingIPBlockingTable, 'get_object')
    def test_cleanup_empty_table_no_rules(self, mock_get_table, mock_get_rules):
        """Test cleanup empty table - no rules."""
        mock_table = self._create_mock_blocking_table()
        mock_get_table.return_value = mock_table
        mock_get_rules.return_value = []

        self.plugin._cleanup_empty_table(self.context, self.floatingip_id)
        
        mock_table.delete.assert_called_once()

    @mock.patch.object(router_obj.FloatingIP, 'get_object')
    def test_create_blocking_rule_success(self, mock_get_fip):
        """Test creating blocking rule - success."""
        mock_fip = self._create_mock_floatingip()
        mock_get_fip.return_value = mock_fip

        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }

        with mock.patch.object(self.plugin, '_get_or_create_blocking_table') as mock_get_table:
            mock_table = self._create_mock_blocking_table()
            mock_get_table.return_value = mock_table

            with mock.patch.object(fip_blocking_obj.FloatingIPBlockingRule) as mock_rule_class:
                mock_rule = self._create_mock_blocking_rule()
                mock_rule_class.return_value = mock_rule

                result = self.plugin.create_blocking_rule(
                    self.context, self.floatingip_id, {'blocking_rule': rule_data})

                self.assertEqual(result, mock_rule)
                mock_rule.create.assert_called_once()


# Rule Validation Tests
class TestRuleValidation(FloatingIPBlockingTestBase):
    """Test rule validation functionality."""

    def setUp(self):
        super(TestRuleValidation, self).setUp()
        self.rule_validator = RuleValidator()

    def test_validate_rule_data_valid_tcp_rule(self):
        """Test validating valid TCP rule."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'remote_ip': '***********/24',
            'remote_port': 8080,
            'direction': 'ingress'
        }
        
        # Should not raise exception
        self.rule_validator.validate_rule_data(rule_data)

    def test_validate_rule_data_invalid_protocol(self):
        """Test validating invalid protocol."""
        rule_data = {
            'protocol': 'invalid',
            'direction': 'ingress'
        }
        
        self.assertRaises(
            fip_blocking_exc.InvalidBlockingRule,
            self.rule_validator.validate_rule_data,
            rule_data
        )

    def test_validate_rule_data_invalid_port(self):
        """Test validating invalid port."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 70000,  # Out of range
            'direction': 'ingress'
        }
        
        self.assertRaises(
            fip_blocking_exc.InvalidBlockingRule,
            self.rule_validator.validate_rule_data,
            rule_data
        )

    def test_validate_rule_data_invalid_ip(self):
        """Test validating invalid IP address."""
        rule_data = {
            'remote_ip': 'invalid-ip',
            'direction': 'ingress'
        }
        
        self.assertRaises(
            fip_blocking_exc.InvalidBlockingRule,
            self.rule_validator.validate_rule_data,
            rule_data
        )

    def test_validate_rule_data_icmp_with_port(self):
        """Test ICMP protocol cannot have ports."""
        rule_data = {
            'protocol': 'icmp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        self.assertRaises(
            fip_blocking_exc.BlockingRuleValidationError,
            self.rule_validator.validate_rule_data,
            rule_data
        )


# Blocking Mode Identification Tests
class TestBlockingModeIdentification(FloatingIPBlockingTestBase):
    """Test seven blocking mode identification."""

    def setUp(self):
        super(TestBlockingModeIdentification, self).setUp()
        self.rule_validator = RuleValidator()

    def test_identify_1_tuple_mode(self):
        """Test 1-tuple mode (FIP only)."""
        rule_data = {'direction': 'ingress'}
        mode = self.rule_validator.identify_blocking_mode(rule_data)
        self.assertEqual(mode, "1-tuple (FIP only)")

    def test_identify_2_tuple_mode(self):
        """Test 2-tuple mode (FIP + Remote IP)."""
        rule_data = {
            'remote_ip': '***********/24',
            'direction': 'ingress'
        }
        mode = self.rule_validator.identify_blocking_mode(rule_data)
        self.assertEqual(mode, "2-tuple (FIP + Remote IP)")

    def test_identify_3_tuple_mode_a(self):
        """Test 3-tuple mode A (FIP + Protocol + Local Port)."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        mode = self.rule_validator.identify_blocking_mode(rule_data)
        self.assertEqual(mode, "3-tuple A (FIP + Protocol + Local Port)")

    def test_identify_5_tuple_mode(self):
        """Test 5-tuple mode (complete)."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'remote_ip': '***********/24',
            'remote_port': 8080,
            'direction': 'ingress'
        }
        mode = self.rule_validator.identify_blocking_mode(rule_data)
        self.assertEqual(mode, "5-tuple (Complete)")


# Conflict Detection Tests
class TestConflictDetection(FloatingIPBlockingTestBase):
    """Test rule conflict detection."""

    def setUp(self):
        super(TestConflictDetection, self).setUp()
        self.rule_validator = RuleValidator()
        self.conflict_detector = ConflictDetector(self.rule_validator)

    def test_detect_rule_conflicts_no_existing_rules(self):
        """Test conflict detection - no existing rules."""
        existing_rules = []
        new_rule_data = {'protocol': 'tcp', 'direction': 'ingress'}
        
        conflicts = self.conflict_detector.detect_rule_conflicts(
            self.context, existing_rules, new_rule_data)
        
        self.assertEqual(conflicts, [])

    def test_detect_rule_conflicts_duplicate_rule(self):
        """Test conflict detection - duplicate rule."""
        existing_rule = self._create_mock_blocking_rule(
            protocol='tcp', local_port=80, direction='ingress')
        existing_rules = [existing_rule]
        
        new_rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        conflicts = self.conflict_detector.detect_rule_conflicts(
            self.context, existing_rules, new_rule_data)
        
        self.assertEqual(len(conflicts), 1)
        self.assertEqual(conflicts[0]['conflict_type'], 'DUPLICATE')

    def test_field_overlap_analysis_identical(self):
        """Test field overlap analysis - identical fields."""
        result = self.conflict_detector._field_overlap_analysis('tcp', 'tcp')
        self.assertTrue(result['overlaps'])
        self.assertEqual(result['relationship'], 'IDENTICAL')

    def test_field_overlap_analysis_disjoint(self):
        """Test field overlap analysis - disjoint fields."""
        result = self.conflict_detector._field_overlap_analysis('tcp', 'udp')
        self.assertFalse(result['overlaps'])
        self.assertEqual(result['relationship'], 'DISJOINT')

    def test_ip_field_overlap_analysis_cidr_overlap(self):
        """Test IP field overlap analysis - CIDR overlap."""
        result = self.conflict_detector._ip_field_overlap_analysis(
            '***********/24', '***********/25')
        self.assertTrue(result['overlaps'])
        self.assertEqual(result['relationship'], 'FIELD1_SUBSUMES')


# Database Integration Tests
class TestDatabaseIntegration(testlib_api.SqlTestCase):
    """Test database integration functionality."""

    def setUp(self):
        super(TestDatabaseIntegration, self).setUp()
        self.context = context.get_admin_context()
        self.plugin = fip_blocking_plugin.FloatingIPBlockingPlugin()
        
        # Mock plugins
        self.l3_plugin = mock.MagicMock()
        directory.add_plugin(plugin_constants.L3, self.l3_plugin)
        
        self.core_plugin = mock.MagicMock()
        directory.add_plugin(None, self.core_plugin)

    def _create_test_floatingip(self, **kwargs):
        """Create test floating IP."""
        fip_data = {
            'id': uuidutils.generate_uuid(),
            'floating_ip_address': '*************',
            'floating_network_id': uuidutils.generate_uuid(),
            'floating_port_id': uuidutils.generate_uuid(),
            'fixed_port_id': None,
            'fixed_ip_address': None,
            'router_id': None,
            'status': constants.FLOATINGIP_STATUS_DOWN,
            'admin_state_up': True,
            'project_id': uuidutils.generate_uuid(),
            'fip_type': constants.FLOATINGIP_TYPE_FIP
        }
        fip_data.update(kwargs)
        
        fip = router_obj.FloatingIP(self.context, **fip_data)
        fip.create()
        return fip

    def test_blocking_table_lifecycle(self):
        """Test blocking table creation and deletion."""
        # Create floating IP
        fip = self._create_test_floatingip()
        
        # Create blocking table
        blocking_table = fip_blocking_obj.FloatingIPBlockingTable(
            self.context,
            floatingip_id=fip.id,
            enabled=True,
            description='Test table',
            project_id=fip.project_id
        )
        blocking_table.create()
        
        # Verify table exists
        retrieved_table = fip_blocking_obj.FloatingIPBlockingTable.get_object(
            self.context, id=blocking_table.id)
        self.assertIsNotNone(retrieved_table)
        self.assertEqual(retrieved_table.floatingip_id, fip.id)
        
        # Delete table
        blocking_table.delete()
        
        # Verify table is deleted
        deleted_table = fip_blocking_obj.FloatingIPBlockingTable.get_object(
            self.context, id=blocking_table.id)
        self.assertIsNone(deleted_table)

    def test_blocking_rule_crud_operations(self):
        """Test blocking rule CRUD operations."""
        # Create floating IP and table
        fip = self._create_test_floatingip()
        blocking_table = fip_blocking_obj.FloatingIPBlockingTable(
            self.context,
            floatingip_id=fip.id,
            enabled=True,
            project_id=fip.project_id
        )
        blocking_table.create()
        
        # Create rule
        rule = fip_blocking_obj.FloatingIPBlockingRule(
            self.context,
            blocking_table_id=blocking_table.id,
            protocol='tcp',
            local_port=80,
            direction='ingress',
            description='Test rule'
        )
        rule.create()
        
        # Read rule
        retrieved_rule = fip_blocking_obj.FloatingIPBlockingRule.get_object(
            self.context, id=rule.id)
        self.assertIsNotNone(retrieved_rule)
        self.assertEqual(retrieved_rule.protocol, 'tcp')
        self.assertEqual(retrieved_rule.local_port, 80)
        
        # Update rule
        rule.local_port = 8080
        rule.update()
        
        updated_rule = fip_blocking_obj.FloatingIPBlockingRule.get_object(
            self.context, id=rule.id)
        self.assertEqual(updated_rule.local_port, 8080)
        
        # Delete rule
        rule.delete()
        
        deleted_rule = fip_blocking_obj.FloatingIPBlockingRule.get_object(
            self.context, id=rule.id)
        self.assertIsNone(deleted_rule)


# Lifecycle Integration Tests
class TestLifecycleIntegration(FloatingIPBlockingTestBase):
    """Test floating IP lifecycle integration."""

    def test_floatingip_delete_cleanup(self):
        """Test floating IP deletion triggers cleanup."""
        # Create mocks
        mock_table = self._create_mock_blocking_table()
        
        with mock.patch.object(self.plugin, '_get_blocking_table') as mock_get_table:
            mock_get_table.return_value = mock_table
            
            # Simulate floating IP deletion event
            payload = events.DBEventPayload(self.context, resource_id=self.floatingip_id)
            registry.notify(resources.FLOATING_IP, events.AFTER_DELETE,
                           self.plugin, payload=payload)
            
            # Verify cleanup was called
            mock_table.delete.assert_called_once()

    def test_floatingip_status_change_sync(self):
        """Test floating IP status change synchronization."""
        mock_table = self._create_mock_blocking_table()
        mock_table.enabled = True
        
        with mock.patch.object(self.plugin, '_get_blocking_table') as mock_get_table:
            mock_get_table.return_value = mock_table
            
            # Simulate status change to DOWN
            old_floatingip = {
                'id': self.floatingip_id,
                'status': constants.FLOATINGIP_STATUS_ACTIVE,
                'admin_state_up': True
            }
            new_floatingip = {
                'id': self.floatingip_id,
                'status': constants.FLOATINGIP_STATUS_DOWN,
                'admin_state_up': True
            }
            
            payload = events.DBEventPayload(self.context, resource_id=self.floatingip_id)
            registry.notify(resources.FLOATING_IP, events.AFTER_UPDATE,
                           self.plugin, payload=payload,
                           floatingip_dict=new_floatingip,
                           old_floatingip=old_floatingip)
            
            # Verify table was disabled
            mock_table.update.assert_called_once()


# Performance and Edge Case Tests
class TestPerformanceAndEdgeCases(FloatingIPBlockingTestBase):
    """Test performance scenarios and edge cases."""

    def test_large_number_of_rules_validation(self):
        """Test validation with many rules."""
        # Create many mock rules
        existing_rules = []
        for i in range(100):
            rule = self._create_mock_blocking_rule(
                id=uuidutils.generate_uuid(),
                local_port=8000 + i
            )
            existing_rules.append(rule)
        
        # Test conflict detection performance
        new_rule_data = {
            'protocol': 'tcp',
            'local_port': 9000,
            'direction': 'ingress'
        }
        
        conflict_detector = ConflictDetector(RuleValidator())
        conflicts = conflict_detector.detect_rule_conflicts(
            self.context, existing_rules, new_rule_data)
        
        # Should not find conflicts with different port
        self.assertEqual(len(conflicts), 0)

    def test_invalid_floating_ip_scenarios(self):
        """Test various invalid floating IP scenarios."""
        invalid_fip_id = uuidutils.generate_uuid()
        
        with mock.patch.object(router_obj.FloatingIP, 'get_object') as mock_get_object:
            mock_get_object.return_value = None
            
            # Test various operations with invalid FIP
            self.assertRaises(
                fip_blocking_exc.FloatingIPNotFound,
                self.plugin._validate_floatingip_exists,
                self.context, invalid_fip_id
            )

    def test_concurrent_rule_operations(self):
        """Test concurrent rule creation scenarios."""
        # This would test race conditions in a real implementation
        # For now, just verify the basic locking mechanism works
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Verify validation doesn't fail under concurrent access
        self.plugin.rule_validator.validate_rule_data(rule_data)
        self.plugin.rule_validator.validate_rule_data(rule_data)


# Detailed Conflict Analysis Tests
class TestDetailedConflictValidation(FloatingIPBlockingTestBase):
    """Test detailed conflict validation functionality."""

    def test_validate_rule_with_detailed_conflicts_valid_rule(self):
        """Test detailed conflict validation - valid rule."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }

        with mock.patch.object(self.plugin, '_detect_rule_conflicts') as mock_detect:
            mock_detect.return_value = []

            report = self.plugin.validate_rule_with_detailed_conflicts(
                self.context, self.floatingip_id, rule_data)

            self.assertTrue(report['valid'])
            self.assertEqual(report['conflict_count'], 0)
            self.assertEqual(report['rule_mode'], "3-tuple A (FIP + Protocol + Local Port)")

    def test_validate_rule_with_detailed_conflicts_with_conflicts(self):
        """Test detailed conflict validation - with conflicts."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }

        mock_conflicts = [{
            'rule_id': 'test-rule-id',
            'conflict_type': 'DUPLICATE',
            'overlap_degree': 5
        }]

        with mock.patch.object(self.plugin, '_detect_rule_conflicts') as mock_detect:
            mock_detect.return_value = mock_conflicts

            report = self.plugin.validate_rule_with_detailed_conflicts(
                self.context, self.floatingip_id, rule_data)

            self.assertFalse(report['valid'])
            self.assertEqual(report['conflict_count'], 1)
            self.assertIn('删除重复规则', report['recommendations'])