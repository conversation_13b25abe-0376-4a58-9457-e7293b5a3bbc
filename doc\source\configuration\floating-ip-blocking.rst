===============================
Floating IP Blocking Plugin
===============================

The Floating IP Blocking plugin provides fine-grained traffic control for floating IPs through 5-tuple based blocking rules.

Configuration
=============

The floating IP blocking plugin is configured in the main neutron configuration file (usually ``/etc/neutron/neutron.conf``).

Enabling the Plugin
-------------------

To enable the floating IP blocking plugin, add it to the ``service_plugins`` list in the ``[DEFAULT]`` section:

.. code-block:: ini

   [DEFAULT]
   service_plugins = router,floating_ip_blocking

Plugin Configuration Options
-----------------------------

The following configuration options are available for the floating IP blocking plugin:

.. code-block:: ini

   [floating_ip_blocking]
   # Enable or disable the floating IP blocking service
   # Default: True
   enabled = True
   
   # Maximum number of blocking rules per floating IP
   # Default: 100
   max_rules_per_fip = 100
   
   # Enable automatic cleanup of empty blocking tables
   # Default: True
   auto_cleanup_empty_tables = True
   
   # Enable rule conflict detection
   # Default: True
   enable_conflict_detection = True
   
   # Default rule priority for new rules
   # Default: 1000
   default_rule_priority = 1000
   
   # Enable audit logging for rule operations
   # Default: False
   enable_audit_logging = False
   
   # Audit log file path (only used if enable_audit_logging is True)
   # Default: /var/log/neutron/floating_ip_blocking_audit.log
   audit_log_file = /var/log/neutron/floating_ip_blocking_audit.log

Database Configuration
----------------------

The floating IP blocking plugin requires database tables to store blocking rules. The database migration will be automatically applied when neutron-db-manage is run.

To manually apply the migration:

.. code-block:: bash

   neutron-db-manage --config-file /etc/neutron/neutron.conf upgrade head

Service Plugin Registration
---------------------------

The plugin is registered in the ``setup.cfg`` file under the ``neutron.service_plugins`` entry point:

.. code-block:: ini

   [entry_points]
   neutron.service_plugins =
       floating_ip_blocking = neutron.services.floating_ip_blocking.plugin:FloatingIPBlockingPlugin

Compatibility with Other Plugins
=================================

L3 Router Plugin
----------------

The floating IP blocking plugin is designed to work seamlessly with the L3 router plugin. It integrates with the floating IP lifecycle management to ensure that blocking rules are properly cleaned up when floating IPs are deleted.

**Configuration Example:**

.. code-block:: ini

   [DEFAULT]
   service_plugins = router,floating_ip_blocking

Security Groups
---------------

The floating IP blocking plugin works independently of security groups but can be used in conjunction with them. Security groups operate at the port level, while floating IP blocking operates at the floating IP level.

**Note:** Rules from both security groups and floating IP blocking will be applied. Ensure that the rules do not conflict with each other.

Firewall as a Service (FWaaS)
-----------------------------

The floating IP blocking plugin can coexist with FWaaS. However, be aware that both services may create iptables rules that could potentially conflict.

**Recommendation:** Use either floating IP blocking or FWaaS for traffic control, not both simultaneously on the same floating IP.

Agent Configuration
===================

L3 Agent
--------

The L3 agent automatically handles floating IP blocking rules when they are created, updated, or deleted. No additional configuration is required on the L3 agent.

The L3 agent will:

- Receive rule notifications from the neutron server
- Convert rules to iptables rules
- Apply rules to the appropriate network namespace
- Clean up rules when floating IPs are deleted

OVS Agent Integration
---------------------

When using Open vSwitch, the floating IP blocking rules are implemented using iptables in the router namespace. The OVS agent does not require special configuration for floating IP blocking.

Performance Considerations
==========================

Rule Limits
-----------

To maintain performance, consider the following limits:

- **Maximum rules per floating IP:** 100 (configurable)
- **Maximum total rules per tenant:** 1000 (recommended)
- **Rule evaluation time:** < 1ms per rule

Database Performance
--------------------

For large deployments with many floating IPs and rules:

- Ensure proper database indexing (automatically created by migrations)
- Consider database connection pooling
- Monitor database query performance

Memory Usage
------------

Each blocking rule consumes approximately:

- **Database storage:** ~200 bytes per rule
- **Agent memory:** ~50 bytes per rule
- **iptables memory:** ~100 bytes per rule

Troubleshooting
===============

Common Issues
-------------

**Plugin not loading:**

Check that the plugin is properly listed in ``service_plugins`` and that the neutron server has been restarted.

**Rules not being applied:**

1. Check that the L3 agent is running and connected to the neutron server
2. Verify that the floating IP exists and is associated with a port
3. Check the L3 agent logs for any errors

**Database migration errors:**

Ensure that the database user has sufficient privileges to create tables and indexes.

Logging
-------

To enable debug logging for the floating IP blocking plugin:

.. code-block:: ini

   [DEFAULT]
   debug = True
   
   [logger_neutron.services.floating_ip_blocking]
   level = DEBUG
   handlers = file
   qualname = neutron.services.floating_ip_blocking

Log Files
---------

- **Neutron server logs:** ``/var/log/neutron/server.log``
- **L3 agent logs:** ``/var/log/neutron/l3-agent.log``
- **Audit logs:** ``/var/log/neutron/floating_ip_blocking_audit.log`` (if enabled)

Example Configuration
=====================

Complete Neutron Configuration
-------------------------------

Here's a complete example of neutron.conf with floating IP blocking enabled:

.. code-block:: ini

   [DEFAULT]
   # Core plugin
   core_plugin = ml2
   
   # Service plugins
   service_plugins = router,floating_ip_blocking
   
   # Database connection
   connection = mysql+pymysql://neutron:password@controller/neutron
   
   # RabbitMQ connection
   transport_url = rabbit://openstack:password@controller
   
   # Keystone authentication
   auth_strategy = keystone
   
   [keystone_authtoken]
   www_authenticate_uri = http://controller:5000
   auth_url = http://controller:5000
   memcached_servers = controller:11211
   auth_type = password
   project_domain_name = default
   user_domain_name = default
   project_name = service
   username = neutron
   password = password
   
   [floating_ip_blocking]
   enabled = True
   max_rules_per_fip = 100
   auto_cleanup_empty_tables = True
   enable_conflict_detection = True
   enable_audit_logging = True
   audit_log_file = /var/log/neutron/floating_ip_blocking_audit.log

L3 Agent Configuration
----------------------

The L3 agent configuration (``/etc/neutron/l3_agent.ini``) does not require special settings for floating IP blocking:

.. code-block:: ini

   [DEFAULT]
   interface_driver = linuxbridge
   # or
   # interface_driver = openvswitch

Migration from Other Solutions
==============================

From Security Groups
---------------------

If you're currently using security groups for floating IP traffic control, you can migrate to floating IP blocking rules:

1. **Identify existing security group rules** that apply to floating IP traffic
2. **Create equivalent blocking rules** using the floating IP blocking API
3. **Test the new rules** to ensure they provide the same protection
4. **Remove the old security group rules** once the new rules are verified

From Custom iptables Rules
---------------------------

If you have custom iptables rules for floating IP traffic control:

1. **Document existing rules** and their purposes
2. **Map rules to blocking rule parameters** (protocol, ports, IPs, direction)
3. **Create blocking rules** using the API
4. **Verify rule application** in the router namespaces
5. **Remove custom iptables rules** once blocking rules are active

Best Practices
==============

Rule Design
-----------

- **Use specific rules** rather than broad rules when possible
- **Document rule purposes** using the description field
- **Group related rules** logically
- **Test rules** in a development environment before applying to production

Security
--------

- **Limit rule creation permissions** to authorized users only
- **Enable audit logging** for compliance requirements
- **Regularly review rules** to ensure they are still needed
- **Use RBAC policies** to control access to floating IP blocking operations

Performance
-----------

- **Avoid overlapping rules** that could cause conflicts
- **Use CIDR notation** for IP ranges rather than individual IP rules
- **Monitor rule evaluation performance** in high-traffic environments
- **Clean up unused rules** regularly