#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import ipaddress

from neutron.db.models import floating_ip_blocking as models
from neutron.objects import base
from neutron.objects import common_types
from neutron.objects import router
from neutron.services.floating_ip_blocking.common import exceptions
from oslo_log import log as logging
from oslo_versionedobjects import fields as obj_fields

LOG = logging.getLogger(__name__)

# Valid protocol values
VALID_PROTOCOLS = ['tcp', 'udp', 'icmp', 'any']


@base.NeutronObjectRegistry.register
class FloatingIPBlockingRule(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = models.FloatingIPBlockingRule

    primary_keys = ['id']

    fields = {
        'id': common_types.UUIDField(),
        'blocking_table_id': common_types.UUIDField(nullable=False),
        'protocol': obj_fields.StringField(nullable=True),
        'local_port': obj_fields.IntegerField(nullable=True),
        'remote_ip': obj_fields.StringField(nullable=True),
        'remote_port': obj_fields.IntegerField(nullable=True),
        'direction': obj_fields.EnumField(
            valid_values=['ingress', 'egress'], nullable=False),
        'enabled': obj_fields.BooleanField(default=True),
        'description': obj_fields.StringField(nullable=True)
    }

    def obj_make_compatible(self, primitive, target_version):
        """Make the primitive representation compatible with target_version."""
        super(FloatingIPBlockingRule, self).obj_make_compatible(
            primitive, target_version)

    def create(self):
        """Create the blocking rule with validation."""
        self._validate_rule()
        with self.db_context_writer(self.obj_context):
            super(FloatingIPBlockingRule, self).create()

    def update(self):
        """Update the blocking rule with validation."""
        self._validate_rule()
        with self.db_context_writer(self.obj_context):
            super(FloatingIPBlockingRule, self).update()

    def _validate_rule(self):
        """Validate the blocking rule fields."""
        # Validate protocol
        if self.protocol and self.protocol.lower() not in VALID_PROTOCOLS:
            raise exceptions.InvalidBlockingRule(
                reason=f"Invalid protocol '{self.protocol}'. "
                       f"Valid values: {', '.join(VALID_PROTOCOLS)}")

        # Validate port ranges
        if self.local_port is not None:
            if not (1 <= self.local_port <= 65535):
                raise exceptions.InvalidBlockingRule(
                    reason=f"Invalid local_port '{self.local_port}'. "
                           "Must be between 1 and 65535")

        if self.remote_port is not None:
            if not (1 <= self.remote_port <= 65535):
                raise exceptions.InvalidBlockingRule(
                    reason=f"Invalid remote_port '{self.remote_port}'. "
                           "Must be between 1 and 65535")

        # Validate remote IP (can be single IP or CIDR)
        if self.remote_ip:
            try:
                ipaddress.ip_network(self.remote_ip, strict=False)
            except ValueError as e:
                raise exceptions.InvalidBlockingRule(
                    reason=f"Invalid remote_ip '{self.remote_ip}': {str(e)}")

        # Validate rule completeness for different blocking modes
        self._validate_blocking_mode()

    def _validate_blocking_mode(self):
        """Validate that the rule represents a valid blocking mode."""
        # Count non-null fields (excluding direction which is always required)
        field_count = sum(1 for field in [self.protocol, self.local_port, 
                                         self.remote_ip, self.remote_port] 
                         if field is not None)
        
        # Mode 1: One-tuple (only floating IP, all fields null)
        if field_count == 0:
            return  # Valid one-tuple mode
            
        # Mode 2: Two-tuple (floating IP + remote IP)
        if field_count == 1 and self.remote_ip is not None:
            return  # Valid two-tuple mode
            
        # Mode 3A: Three-tuple (floating IP + protocol + local port)
        if (field_count == 2 and self.protocol is not None and 
            self.local_port is not None):
            return  # Valid three-tuple mode A
            
        # Mode 3B: Three-tuple (floating IP + protocol + remote port)
        if (field_count == 2 and self.protocol is not None and 
            self.remote_port is not None):
            return  # Valid three-tuple mode B
            
        # Mode 4A: Four-tuple (floating IP + protocol + local port + remote IP)
        if (field_count == 3 and self.protocol is not None and 
            self.local_port is not None and self.remote_ip is not None):
            return  # Valid four-tuple mode A
            
        # Mode 4B: Four-tuple (floating IP + protocol + remote IP + remote port)
        if (field_count == 3 and self.protocol is not None and 
            self.remote_ip is not None and self.remote_port is not None):
            return  # Valid four-tuple mode B
            
        # Mode 5: Five-tuple (all fields present)
        if field_count == 4:
            return  # Valid five-tuple mode
            
        # Invalid combination
        raise exceptions.InvalidBlockingRule(
            reason="Invalid field combination. Rule must match one of the "
                   "seven supported blocking modes (1-tuple to 5-tuple)")

    @classmethod
    def get_rules_by_blocking_table(cls, context, blocking_table_id):
        """Get all rules for a specific blocking table."""
        return cls.get_objects(context, blocking_table_id=blocking_table_id)

    @classmethod
    def delete_rules_by_blocking_table(cls, context, blocking_table_id):
        """Delete all rules for a specific blocking table."""
        return cls.delete_objects(context, blocking_table_id=blocking_table_id)

    def to_dict(self):
        """Convert the rule to a dictionary for API responses."""
        return {
            'id': self.id,
            'blocking_table_id': self.blocking_table_id,
            'protocol': self.protocol,
            'local_port': self.local_port,
            'remote_ip': self.remote_ip,
            'remote_port': self.remote_port,
            'direction': self.direction,
            'enabled': self.enabled,
            'description': self.description,
            'created_at': getattr(self, 'created_at', None),
            'updated_at': getattr(self, 'updated_at', None)
        }


@base.NeutronObjectRegistry.register
class FloatingIPBlockingTable(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = models.FloatingIPBlockingTable

    primary_keys = ['id']
    foreign_keys = {'FloatingIP': {'floatingip_id': 'id'}}

    fields = {
        'id': common_types.UUIDField(),
        'floatingip_id': common_types.UUIDField(nullable=False),
        'enabled': obj_fields.BooleanField(default=True),
        'description': obj_fields.StringField(nullable=True),
        'project_id': obj_fields.StringField()
    }

    fields_no_update = {'id', 'floatingip_id', 'project_id'}
    synthetic_fields = ['floating_ip_address', 'rules_count']

    def obj_make_compatible(self, primitive, target_version):
        """Make the primitive representation compatible with target_version."""
        super(FloatingIPBlockingTable, self).obj_make_compatible(
            primitive, target_version)

    def create(self):
        """Create the blocking table with validation."""
        self._validate_floatingip_exists()
        with self.db_context_writer(self.obj_context):
            super(FloatingIPBlockingTable, self).create()

    def update(self):
        """Update the blocking table."""
        with self.db_context_writer(self.obj_context):
            super(FloatingIPBlockingTable, self).update()

    def delete(self):
        """Delete the blocking table and all associated rules."""
        with self.db_context_writer(self.obj_context):
            # Delete all associated rules first
            FloatingIPBlockingRule.delete_rules_by_blocking_table(
                self.obj_context, self.id)
            super(FloatingIPBlockingTable, self).delete()

    def _validate_floatingip_exists(self):
        """Validate that the associated floating IP exists."""
        fip = router.FloatingIP.get_object(self.obj_context, 
                                          id=self.floatingip_id)
        if not fip:
            raise exceptions.FloatingIPNotFound(fip_id=self.floatingip_id)

    def from_db_object(self, db_obj):
        """Load additional data when creating object from database."""
        super(FloatingIPBlockingTable, self).from_db_object(db_obj)
        
        # Load floating IP address
        try:
            fip = router.FloatingIP.get_object(self.obj_context,
                                              id=self.floatingip_id)
            if fip:
                setattr(self, 'floating_ip_address', fip.floating_ip_address)
            else:
                setattr(self, 'floating_ip_address', None)
        except Exception as e:
            LOG.warning("Failed to load floating IP address for %s: %s",
                       self.floatingip_id, str(e))
            setattr(self, 'floating_ip_address', None)
        
        # Load rules count
        rules_count = FloatingIPBlockingRule.count(
            self.obj_context, blocking_table_id=self.id)
        setattr(self, 'rules_count', rules_count)
        
        self.obj_reset_changes(['floating_ip_address', 'rules_count'])

    @classmethod
    def get_by_floatingip_id(cls, context, floatingip_id):
        """Get blocking table by floating IP ID."""
        return cls.get_object(context, floatingip_id=floatingip_id)

    @classmethod
    def get_or_create_by_floatingip_id(cls, context, floatingip_id, 
                                      project_id, description=None):
        """Get existing blocking table or create a new one."""
        blocking_table = cls.get_by_floatingip_id(context, floatingip_id)
        if blocking_table:
            return blocking_table, False
            
        # Create new blocking table
        blocking_table = cls(
            context,
            floatingip_id=floatingip_id,
            project_id=project_id,
            enabled=True,
            description=description or f"Auto-created for FIP {floatingip_id}"
        )
        blocking_table.create()
        return blocking_table, True

    def get_rules(self):
        """Get all rules associated with this blocking table."""
        return FloatingIPBlockingRule.get_rules_by_blocking_table(
            self.obj_context, self.id)

    def has_rules(self):
        """Check if the blocking table has any rules."""
        return FloatingIPBlockingRule.count(
            self.obj_context, blocking_table_id=self.id) > 0

    def cleanup_if_empty(self):
        """Delete the blocking table if it has no rules."""
        if not self.has_rules():
            self.delete()
            return True
        return False

    def to_dict(self):
        """Convert the table to a dictionary for API responses."""
        return {
            'id': self.id,
            'floatingip_id': self.floatingip_id,
            'enabled': self.enabled,
            'description': self.description,
            'project_id': self.project_id,
            'floating_ip_address': getattr(self, 'floating_ip_address', None),
            'rules_count': getattr(self, 'rules_count', 0),
            'created_at': getattr(self, 'created_at', None),
            'updated_at': getattr(self, 'updated_at', None)
        }