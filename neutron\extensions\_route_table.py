#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api import converters
from neutron_lib.api.definitions import l3
from neutron_lib.db import constants as db_const

# The name of the extension.
NAME = 'Route Table'
ROUTE_TABLE = "ROUTE_TABLE"

# The name of the sub-extension
ROUTE_TABLE_ROUTE = "ROUTE_TABLE_ROUTE"
ROUTE_TABLE_SUBNET_BINDING = "ROUTE_TABLE_SUBNET_BINDING"
ROUTE_TABLE_GATEWAY_BINDING = "ROUTE_TABLE_GATEWAY_BINDING"

# The alias of the extension.
ALIAS = 'route-table'
# The description of the extension.
DESCRIPTION = "Route table extension"

# A timestamp of when the extension was introduced.
UPDATED_TIMESTAMP = "2021-01-16T10:08:08-08:08"

# The name of the resource.
RESOURCE_NAME = 'route_table'

# The plural for the resource.
COLLECTION_NAME = 'route_tables'

# The associate type of the resource.
ROUTETABLE_ASSOCIATE_TYPE = ['subnet', 'gateway']

RESOURCE_ATTRIBUTE_MAP = {
    COLLECTION_NAME: {
        'id': {'allow_post': False,
               'allow_put': False,
               'validate': {'type:uuid': None},
               'is_visible': True,
               'is_sort_key': True,
               'primary_key': True},
        'name': {'allow_post': True, 'allow_put': True,
                 'validate': {'type:string': db_const.NAME_FIELD_SIZE},
                 'is_sort_key': True,
                 'is_visible': True,
                 'default': ''},
        'router_id': {'allow_post': True,
                      'allow_put': False,
                      'validate': {'type:uuid': None},
                      'is_sort_key': True,
                      'is_visible': True},
        'tenant_id': {'allow_post': True,
                      'allow_put': False,
                      'validate': {
                          'type:string': db_const.PROJECT_ID_FIELD_SIZE},
                      'required_by_policy': True,
                      'is_visible': True},
        'routes': {
            'allow_post': True, 'allow_put': True,
            'validate': {'type:routetableroutes': None},
            'convert_to': converters.convert_none_to_empty_list,
            'is_visible': True,
            'default': []},
        'bound_subnets': {'allow_post': True, 'allow_put': True,
                          'convert_to': converters.convert_none_to_empty_list,
                          'is_visible': True,
                          'default': []},
        'bound_router': {'allow_post': True, 'allow_put': True,
                         'convert_to': converters.convert_none_to_empty_list,
                         'is_visible': True,
                         'default': []},
        'default': {'allow_post': False, 'allow_put': False,
                    'default': False,
                    'convert_to': converters.convert_to_boolean,
                    'is_sort_key': True,
                    'is_visible': True},
        'table_id': {'allow_post': False,
                     'allow_put': False,
                     'is_visible': True},
        'associate_type': {
            'allow_post': True,
            'allow_put': False,
            'is_visible': True,
            'validate': {'type:values': ROUTETABLE_ASSOCIATE_TYPE},
            'default': 'subnet'},
    }
}

# The list of required extensions.
REQUIRED_EXTENSIONS = [l3.ALIAS]

# Whether or not this extension is simply signaling behavior to the user
# or it actively modifies the attribute map.
IS_SHIM_EXTENSION = False

# Whether the extension is marking the adoption of standardattr model for
# legacy resources, or introducing new standardattr attributes. False or
# None if the standardattr model is adopted since the introduction of
# resource extension.
# If this is True, the alias for the extension should be prefixed with
# 'standard-attr-'.
IS_STANDARD_ATTR_EXTENSION = False

# The subresource attribute map for the extension. It adds child resources
# to main extension's resource. The subresource map must have a parent and
# a parameters entry. If an extension does not need such a map, None can
# be specified (mandatory).
SUB_RESOURCE_ATTRIBUTE_MAP = {}

# The action map: it associates verbs with methods to be performed on
# the API resource.
ACTION_MAP = {
    RESOURCE_NAME: {
        'associate_subnets': 'PUT',
        'disassociate_subnets': 'PUT',
        'add_route_table_routes': 'PUT',
        'update_route_table_routes': 'PUT',
        'remove_route_table_routes': 'PUT',
        'get_route_table_routes': 'PUT',
        'associate_gateway': 'PUT',
        'disassociate_gateway': 'PUT',
        'sync_route_table': 'PUT',
    }
}

# The list of required extensions.
REQUIRED_EXTENSIONS = []

# The list of optional extensions.
OPTIONAL_EXTENSIONS = []

# The action status.
ACTION_STATUS = {}
