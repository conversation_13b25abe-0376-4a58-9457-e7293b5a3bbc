#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import functools

from neutron_lib.callbacks import events
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import constants
from neutron_lib.db import utils as db_utils
from neutron_lib.objects import exceptions as obj_exc
from neutron_lib.plugins import constants as plugin_constants
from neutron_lib.plugins import directory
from oslo_db import exception as db_exc
from oslo_log import log as logging

from neutron.db import api as db_api
from neutron.db import db_base_plugin_common
from neutron.objects import floating_ip_blocking as fip_blocking_obj
from neutron.objects import router as router_obj
from neutron.services.floating_ip_blocking.common import exceptions as fip_blocking_exc
from neutron.services.floating_ip_blocking.rule_validator import RuleValidator
from neutron.services.floating_ip_blocking.rule_validator import ConflictDetector

LOG = logging.getLogger(__name__)


def make_result_with_fields(f):
    @functools.wraps(f)
    def inner(*args, **kwargs):
        fields = kwargs.get('fields')
        result = f(*args, **kwargs)
        if fields is None:
            return result
        elif isinstance(result, list):
            return [db_utils.resource_fields(r, fields) for r in result]
        else:
            return db_utils.resource_fields(result, fields)
    return inner


@registry.has_registry_receivers
class FloatingIPBlockingPlugin:
    """浮动IP阻断插件，实现自动化表生命周期管理"""

    def __init__(self):
        super(FloatingIPBlockingPlugin, self).__init__()
        self.l3_plugin = directory.get_plugin(plugin_constants.L3)
        self.core_plugin = directory.get_plugin()
        
        # Initialize validation components
        self.rule_validator = RuleValidator()
        self.conflict_detector = ConflictDetector(self.rule_validator)

    def _get_floatingip(self, context, floatingip_id):
        """获取浮动IP对象"""
        return router_obj.FloatingIP.get_object(context, id=floatingip_id)

    def _validate_floatingip_exists(self, context, floatingip_id):
        """验证浮动IP存在性"""
        fip = self._get_floatingip(context, floatingip_id)
        if not fip:
            raise fip_blocking_exc.FloatingIPNotFound(fip_id=floatingip_id)
        return fip

    def _get_blocking_table(self, context, floatingip_id):
        """获取阻断表"""
        return fip_blocking_obj.FloatingIPBlockingTable.get_object(
            context, floatingip_id=floatingip_id)

    def _get_or_create_blocking_table(self, context, floatingip_id, create_if_missing=False):
        """获取或创建阻断表（延迟创建机制）"""
        # 1. 尝试获取现有表
        blocking_table = self._get_blocking_table(context, floatingip_id)
        if blocking_table:
            return blocking_table

        # 2. 仅在明确需要创建时才创建表
        if create_if_missing:
            # 验证浮动IP存在
            fip = self._validate_floatingip_exists(context, floatingip_id)
            
            blocking_table = fip_blocking_obj.FloatingIPBlockingTable(
                context,
                floatingip_id=floatingip_id,
                enabled=True,
                description=f"Auto-created blocking table for FIP {floatingip_id}",
                project_id=fip.project_id
            )
            blocking_table.create()
            return blocking_table

        return None

    def _cleanup_empty_table(self, context, floatingip_id):
        """清理空的阻断表"""
        blocking_table = self._get_blocking_table(context, floatingip_id)
        if blocking_table:
            # 检查是否还有规则
            rules = fip_blocking_obj.FloatingIPBlockingRule.get_objects(
                context, blocking_table_id=blocking_table.id)
            if not rules:
                blocking_table.delete()

    def _get_blocking_rule(self, context, rule_id):
        """获取阻断规则"""
        rule = fip_blocking_obj.FloatingIPBlockingRule.get_object(context, id=rule_id)
        if not rule:
            raise fip_blocking_exc.BlockingRuleNotFound(rule_id=rule_id)
        return rule

    def _detect_rule_conflicts(self, context, floatingip_id, new_rule_data, exclude_rule_id=None):
        """检测规则冲突 - 使用增强的五元组重叠检测算法"""
        blocking_table = self._get_blocking_table(context, floatingip_id)
        if not blocking_table:
            return []  # 新表，无冲突

        # 获取现有规则
        existing_rules = fip_blocking_obj.FloatingIPBlockingRule.get_objects(
            context, blocking_table_id=blocking_table.id)
        
        # 使用冲突检测器进行检测
        return self.conflict_detector.detect_rule_conflicts(
            context, existing_rules, new_rule_data, exclude_rule_id)

    def validate_rule_with_detailed_conflicts(self, context, floatingip_id, rule_data, exclude_rule_id=None):
        """验证规则并返回详细的冲突分析报告"""
        # 基础验证
        self.rule_validator.validate_rule_data(rule_data)
        
        # 冲突检测
        conflicts = self._detect_rule_conflicts(context, floatingip_id, rule_data, exclude_rule_id)
        
        # 生成详细报告
        validation_report = {
            'valid': len(conflicts) == 0,
            'rule_mode': self.rule_validator.identify_blocking_mode(rule_data),
            'conflicts': conflicts,
            'conflict_count': len(conflicts),
            'recommendations': []
        }

        # 生成推荐建议
        if conflicts:
            validation_report['recommendations'] = [
                "检查现有规则是否可以合并",
                "考虑使用更具体的五元组字段",
                "验证规则的业务逻辑是否正确"
            ]
            
            # 根据冲突类型添加特定建议
            conflict_types = [c['conflict_type'] for c in conflicts]
            if 'DUPLICATE' in conflict_types:
                validation_report['recommendations'].append("删除重复规则")
            if any('SUBSUMES' in ct for ct in conflict_types):
                validation_report['recommendations'].append("调整规则范围避免包含关系")

        return validation_report

    @fip_blocking_exc.handle_database_errors
    @fip_blocking_exc.retry_on_db_error(max_retries=3)
    @db_base_plugin_common.convert_result_to_dict
    def create_blocking_rule(self, context, floatingip_id, blocking_rule):
        """创建阻断规则（如需要则自动创建表）"""
        rule_data = blocking_rule.get('blocking_rule', blocking_rule)

        try:
            # 1. 验证浮动IP存在性
            self._validate_floatingip_exists(context, floatingip_id)

            # 2. 验证规则数据
            self.rule_validator.validate_rule_data(rule_data)

            # 3. 检测规则冲突
            conflicts = self._detect_rule_conflicts(context, floatingip_id, rule_data)
            if conflicts:
                # 抛出详细的冲突异常
                raise fip_blocking_exc.create_conflict_exception(conflicts)

            # 4. 获取或创建阻断表（延迟创建）
            blocking_table = self._get_or_create_blocking_table(
                context, floatingip_id, create_if_missing=True)

            # 5. 创建规则记录
            with db_api.context_manager.writer.using(context):
                rule_obj = fip_blocking_obj.FloatingIPBlockingRule(
                    context,
                    blocking_table_id=blocking_table.id,
                    **rule_data
                )
                rule_obj.create()

            # 6. 记录操作日志
            fip_blocking_exc.log_exception_details(
                None, context=context, operation='create_rule',
                floatingip_id=floatingip_id, rule_id=rule_obj.id,
                rule_data=rule_data)

            return rule_obj

        except fip_blocking_exc.FloatingIPBlockingException:
            # 重新抛出我们自己的异常
            raise
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='create_rule',
                floatingip_id=floatingip_id, rule_data=rule_data)
            raise fip_blocking_exc.BlockingRuleConflict(
                conflict_rule_id="duplicate", conflict_type="DUPLICATE",
                suggestion="Rule with identical parameters already exists")
        except db_exc.DBReferenceError as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='create_rule',
                floatingip_id=floatingip_id, rule_data=rule_data)
            raise fip_blocking_exc.FailedToCreateOrUpdateBlockingRule(
                error="Blocking table does not exist or has been removed")
        except Exception as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='create_rule',
                floatingip_id=floatingip_id, rule_data=rule_data)
            raise fip_blocking_exc.FailedToCreateOrUpdateBlockingRule(
                error=str(e))

    @fip_blocking_exc.handle_database_errors
    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def get_blocking_rules(self, context, floatingip_id, fields=None):
        """获取浮动IP的所有阻断规则"""
        try:
            # 1. 验证浮动IP存在性
            self._validate_floatingip_exists(context, floatingip_id)

            # 2. 获取阻断表（不存在则返回空列表）
            blocking_table = self._get_blocking_table(context, floatingip_id)
            if not blocking_table:
                return []

            # 3. 返回规则列表
            return fip_blocking_obj.FloatingIPBlockingRule.get_objects(
                context, blocking_table_id=blocking_table.id)

        except fip_blocking_exc.FloatingIPBlockingException:
            # 重新抛出我们自己的异常
            raise
        except Exception as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='get_rules',
                floatingip_id=floatingip_id)
            raise fip_blocking_exc.DatabaseOperationFailed(
                operation='get_blocking_rules', error=str(e))

    @fip_blocking_exc.handle_database_errors
    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def get_blocking_rule(self, context, floatingip_id, rule_id, fields=None):
        """获取指定的阻断规则"""
        try:
            # 验证浮动IP存在性
            self._validate_floatingip_exists(context, floatingip_id)
            
            # 获取规则
            rule = self._get_blocking_rule(context, rule_id)
            
            # 验证规则属于指定的浮动IP
            blocking_table = self._get_blocking_table(context, floatingip_id)
            if not blocking_table or rule.blocking_table_id != blocking_table.id:
                raise fip_blocking_exc.BlockingRuleNotFound(rule_id=rule_id)
            
            return rule

        except fip_blocking_exc.FloatingIPBlockingException:
            # 重新抛出我们自己的异常
            raise
        except Exception as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='get_rule',
                floatingip_id=floatingip_id, rule_id=rule_id)
            raise fip_blocking_exc.DatabaseOperationFailed(
                operation='get_blocking_rule', error=str(e))

    @fip_blocking_exc.handle_database_errors
    @fip_blocking_exc.retry_on_db_error(max_retries=3)
    @db_base_plugin_common.convert_result_to_dict
    def update_blocking_rule(self, context, floatingip_id, rule_id, blocking_rule):
        """更新阻断规则"""
        rule_data = blocking_rule.get('blocking_rule', blocking_rule)

        try:
            # 1. 验证浮动IP存在性
            self._validate_floatingip_exists(context, floatingip_id)

            # 2. 获取现有规则
            rule = self._get_blocking_rule(context, rule_id)

            # 3. 验证规则属于指定的浮动IP
            blocking_table = self._get_blocking_table(context, floatingip_id)
            if not blocking_table or rule.blocking_table_id != blocking_table.id:
                raise fip_blocking_exc.BlockingRuleNotFound(rule_id=rule_id)

            # 4. 验证规则数据
            self.rule_validator.validate_rule_data(rule_data)

            # 5. 检测规则冲突（排除当前规则）
            conflicts = self._detect_rule_conflicts(
                context, floatingip_id, rule_data, exclude_rule_id=rule_id)
            if conflicts:
                raise fip_blocking_exc.create_conflict_exception(conflicts)

            # 6. 更新规则
            with db_api.context_manager.writer.using(context):
                rule.update_fields(rule_data)
                rule.update()

            # 7. 记录操作日志
            fip_blocking_exc.log_exception_details(
                None, context=context, operation='update_rule',
                floatingip_id=floatingip_id, rule_id=rule_id,
                rule_data=rule_data)

            return rule

        except fip_blocking_exc.FloatingIPBlockingException:
            # 重新抛出我们自己的异常
            raise
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='update_rule',
                floatingip_id=floatingip_id, rule_id=rule_id, rule_data=rule_data)
            raise fip_blocking_exc.BlockingRuleConflict(
                conflict_rule_id="duplicate", conflict_type="DUPLICATE",
                suggestion="Rule with identical parameters already exists")
        except db_exc.DBReferenceError as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='update_rule',
                floatingip_id=floatingip_id, rule_id=rule_id, rule_data=rule_data)
            raise fip_blocking_exc.FailedToCreateOrUpdateBlockingRule(
                error="Blocking table does not exist or has been removed")
        except Exception as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='update_rule',
                floatingip_id=floatingip_id, rule_id=rule_id, rule_data=rule_data)
            raise fip_blocking_exc.FailedToCreateOrUpdateBlockingRule(
                error=str(e))

    @fip_blocking_exc.handle_database_errors
    @fip_blocking_exc.retry_on_db_error(max_retries=3)
    def delete_blocking_rule(self, context, floatingip_id, rule_id):
        """删除阻断规则（自动清理空表）"""
        try:
            # 1. 验证浮动IP存在性
            self._validate_floatingip_exists(context, floatingip_id)

            # 2. 获取规则
            rule = self._get_blocking_rule(context, rule_id)

            # 3. 验证规则属于指定的浮动IP
            blocking_table = self._get_blocking_table(context, floatingip_id)
            if not blocking_table or rule.blocking_table_id != blocking_table.id:
                raise fip_blocking_exc.BlockingRuleNotFound(rule_id=rule_id)

            # 4. 删除规则
            with db_api.context_manager.writer.using(context):
                rule.delete()

            # 5. 检查表是否为空，如果为空则删除表
            self._cleanup_empty_table(context, floatingip_id)

            # 6. 记录操作日志
            fip_blocking_exc.log_exception_details(
                None, context=context, operation='delete_rule',
                floatingip_id=floatingip_id, rule_id=rule_id)

        except fip_blocking_exc.FloatingIPBlockingException:
            # 重新抛出我们自己的异常
            raise
        except Exception as e:
            fip_blocking_exc.log_exception_details(
                e, context=context, operation='delete_rule',
                floatingip_id=floatingip_id, rule_id=rule_id)
            raise fip_blocking_exc.DatabaseOperationFailed(
                operation='delete_blocking_rule', error=str(e))

    @registry.receives(resources.FLOATING_IP, [events.AFTER_DELETE])
    def on_floatingip_delete(self, resource, event, trigger, payload=None, **kwargs):
        """浮动IP删除后清理阻断表"""
        try:
            # 支持新旧两种回调格式
            if payload:
                context = payload.context
                floatingip_id = payload.resource_id
            else:
                context = kwargs.get('context')
                floatingip_id = kwargs.get('id')

            if not context or not floatingip_id:
                LOG.warning("Missing context or floatingip_id in callback")
                return

            # 获取并删除阻断表（级联删除会处理规则）
            blocking_table = self._get_blocking_table(context, floatingip_id)
            if blocking_table:
                blocking_table.delete()
                LOG.info("Cleaned up blocking table for floating IP %s", floatingip_id)

        except Exception as e:
            # 不要让回调异常影响浮动IP删除
            LOG.error("Failed to cleanup blocking table for floating IP %s: %s",
                     floatingip_id, str(e))

    @registry.receives(resources.FLOATING_IP, [events.AFTER_UPDATE])
    def on_floatingip_update(self, resource, event, trigger, payload=None, **kwargs):
        """浮动IP状态变更时同步规则状态"""
        try:
            # 支持新旧两种回调格式
            if payload:
                context = payload.context
                floatingip_id = payload.resource_id
                new_floatingip = kwargs.get('floatingip_dict', {})
                old_floatingip = kwargs.get('old_floatingip', {})
            else:
                context = kwargs.get('context')
                floatingip_id = kwargs.get('id')
                new_floatingip = kwargs.get('floatingip_dict', {})
                old_floatingip = kwargs.get('old_floatingip', {})

            if not context or not floatingip_id:
                return

            # 检查状态是否发生变化
            old_status = old_floatingip.get('status')
            new_status = new_floatingip.get('status')
            old_admin_state = old_floatingip.get('admin_state_up')
            new_admin_state = new_floatingip.get('admin_state_up')

            if old_status == new_status and old_admin_state == new_admin_state:
                return  # 状态未变化

            # 获取阻断表
            blocking_table = self._get_blocking_table(context, floatingip_id)
            if not blocking_table:
                return  # 没有阻断表

            # 确定新的启用状态
            should_enable = (new_status == constants.FLOATINGIP_STATUS_ACTIVE and 
                           new_admin_state is True)

            # 更新阻断表状态
            if blocking_table.enabled != should_enable:
                blocking_table.enabled = should_enable
                blocking_table.update()
                LOG.info("Updated blocking table status for floating IP %s: %s",
                        floatingip_id, should_enable)

        except Exception as e:
            # 不要让回调异常影响浮动IP更新
            LOG.error("Failed to sync blocking table status for floating IP %s: %s",
                     floatingip_id, str(e))