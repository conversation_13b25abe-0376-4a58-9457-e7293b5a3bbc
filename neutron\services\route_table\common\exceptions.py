#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron._i18n import _
from neutron_lib import exceptions as n_exc


class RouteTableNotFound(n_exc.NotFound):
    message = _("Route table %(id)s could not be found.")


class RouteTablesExhausted(n_exc.BadRequest):
    message = _("Unable to complete operation."
                "The number of Route table exceeds the limit %(quota)s.")


class RouteTableInUse(n_exc.InUse):
    message = _("Route table %(id)s is associated with subnet.")


class GatewayRouteTableInUse(n_exc.InUse):
    message = _("Route table %(id)s is associated with gateway.")


class RouterInterfaceInUseByRouteTableRoutes(n_exc.InUse):
    message = _("Router interface for subnet %(subnet_id)s on router "
                "%(router_id)s cannot be deleted, as it is required "
                "by one or more route tables.")


class RouterGatewayInUseByGatewayRouteTable(n_exc.InUse):
    message = _("Router gateway cannot be deleted, as it is required "
                "by gateway route table.")


class InvalidOperationForDefaultRouteTable(n_exc.BadRequest):
    message = _("Unable to complete operation for Default route table %(id)s, "
                "%(reason)s.")


class RouteTableRoutesExhausted(n_exc.BadRequest):
    message = _("Unable to complete operation for %(routetable_id)s. "
                "The number of Routes exceeds the limit %(quota)s.")


class RouteTableRoutesNotFound(n_exc.NotFound):
    message = _("Route table route %(route)s could not be found.")


class RoutesNexthopTypeNotSupport(n_exc.InvalidInput):
    message = _("Route nexthop type %(nexthop)s not support.")


class InvalidRoutes(n_exc.InvalidInput):
    message = _("Invalid format for routes: %(routes)s, %(reason)s.")


class FailedToAssociateSubnets(n_exc.BadRequest):
    message = _("Failed to associate subnets, error: %(error)s.")


class FailedToDisassociateSubnets(n_exc.BadRequest):
    message = _("Failed to disassociate subnets, error: %(error)s.")


class FailedToCreateOrUpdateRouteTable(n_exc.BadRequest):
    message = _("Failed to create or update Route Table, error: %(error)s.")


class FailedToAssociateGateway(n_exc.BadRequest):
    message = _("Failed to associate gateway, error: %(error)s.")


class FailedToDisassociateGateway(n_exc.BadRequest):
    message = _("Failed to disassociate gateway, error: %(error)s.")


class GatewayRouteTableExisted(n_exc.BadRequest):
    message = _("The router already has a gateway route table")
