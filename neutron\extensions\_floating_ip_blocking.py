#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

from neutron_lib.api import converters
from neutron_lib.api.definitions import l3
from neutron_lib.db import constants as db_const

# The name of the extension.
NAME = 'Floating IP Traffic Blocking'
FLOATING_IP_BLOCKING = "FLOATING_IP_BLOCKING"

# The alias of the extension.
ALIAS = 'floating-ip-blocking'

# The description of the extension.
DESCRIPTION = "Extension to control floating IP traffic blocking with fine-grained rules"

# A timestamp of when the extension was introduced.
UPDATED_TIMESTAMP = "2025-01-19T10:00:00-00:00"

# The name of the resource.
RESOURCE_NAME = 'blocking_rule'

# The plural for the resource.
COLLECTION_NAME = 'blocking_rules'

# Parent resource information
PARENT_RESOURCE_NAME = 'floatingip'
PARENT_COLLECTION_NAME = 'floatingips'

# API prefix
API_PREFIX = '/floatingips'

# Protocol choices
PROTOCOL_CHOICES = ['tcp', 'udp', 'icmp', 'any']

# Direction choices  
DIRECTION_CHOICES = ['ingress', 'egress']

# IP address or CIDR format regex
IP_REGEX = (r'^(([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])\.){3}'
           r'([0-9]|[1-9][0-9]|1[0-9]{2}|2[0-4][0-9]|25[0-5])'
           r'(\/([0-9]|[1-2][0-9]|3[0-2]))?$')

# Main resource attribute map (empty for sub-resources)
RESOURCE_ATTRIBUTE_MAP = {}

# Blocking rule attributes
BLOCKING_RULE_ATTRIBUTES = {
    'id': {
        'allow_post': False,
        'allow_put': False,
        'validate': {'type:uuid': None},
        'is_visible': True,
        'is_sort_key': True,
        'primary_key': True
    },
    'floatingip_id': {
        'allow_post': False,
        'allow_put': False,
        'validate': {'type:uuid': None},
        'is_visible': True,
        'is_sort_key': True
    },
    'protocol': {
        'allow_post': True,
        'allow_put': True,
        'validate': {'type:values': PROTOCOL_CHOICES},
        'is_visible': True,
        'is_sort_key': True,
        'default': None
    },
    'local_port': {
        'allow_post': True,
        'allow_put': True,
        'validate': {'type:range_or_none': [1, 65535]},
        'convert_to': converters.convert_to_int,
        'is_visible': True,
        'is_sort_key': True,
        'default': None
    },
    'remote_ip': {
        'allow_post': True,
        'allow_put': True,
        'validate': {'type:regex_or_none': IP_REGEX},
        'is_visible': True,
        'is_sort_key': True,
        'default': None
    },
    'remote_port': {
        'allow_post': True,
        'allow_put': True,
        'validate': {'type:range_or_none': [1, 65535]},
        'convert_to': converters.convert_to_int,
        'is_visible': True,
        'is_sort_key': True,
        'default': None
    },
    'direction': {
        'allow_post': True,
        'allow_put': True,
        'validate': {'type:values': DIRECTION_CHOICES},
        'is_visible': True,
        'is_sort_key': True,
        'required_by_policy': False
    },
    'description': {
        'allow_post': True,
        'allow_put': True,
        'validate': {'type:string': db_const.DESCRIPTION_FIELD_SIZE},
        'is_visible': True,
        'is_sort_key': True,
        'default': ''
    },
    'project_id': {
        'allow_post': False,
        'allow_put': False,
        'validate': {'type:string': db_const.PROJECT_ID_FIELD_SIZE},
        'required_by_policy': True,
        'is_visible': True
    },
    'created_at': {
        'allow_post': False,
        'allow_put': False,
        'is_visible': True
    },
    'updated_at': {
        'allow_post': False,
        'allow_put': False,
        'is_visible': True
    }
}

# The subresource attribute map for the extension
SUB_RESOURCE_ATTRIBUTE_MAP = {
    COLLECTION_NAME: {
        'parent': {
            'collection_name': PARENT_COLLECTION_NAME,
            'member_name': PARENT_RESOURCE_NAME
        },
        'parameters': BLOCKING_RULE_ATTRIBUTES
    }
}

# The action map
ACTION_MAP = {}

# The list of required extensions
REQUIRED_EXTENSIONS = [l3.ALIAS]

# The list of optional extensions
OPTIONAL_EXTENSIONS = []

# Whether or not this extension is simply signaling behavior to the user
# or it actively modifies the attribute map
IS_SHIM_EXTENSION = False

# Whether the extension is marking the adoption of standardattr model for
# legacy resources, or introducing new standardattr attributes
IS_STANDARD_ATTR_EXTENSION = False

# The action status
ACTION_STATUS = {}