=======================================
Floating IP Blocking Complete Guide
=======================================

This comprehensive guide covers installation, configuration, API usage, and troubleshooting for the Floating IP Blocking service in OpenStack Neutron.

.. contents::
   :local:
   :depth: 3

Overview
========

The Floating IP Blocking service provides fine-grained traffic control for floating IPs through 5-tuple based blocking rules. It supports seven different blocking modes, from simple 1-tuple (floating IP only) to complete 5-tuple specifications.

Key Features
------------

- **Seven Blocking Modes**: Support for 1-tuple to 5-tuple blocking specifications
- **Automatic Lifecycle Management**: Lazy table creation and automatic cleanup
- **Conflict Detection**: Advanced 5-tuple overlap detection algorithm
- **RESTful API**: Standard OpenStack API design patterns
- **Integration**: Seamless integration with L3 router plugin

Installation and Setup
======================

Prerequisites
-------------

Before installing the Floating IP Blocking plugin, ensure that you have:

- OpenStack Neutron installed and configured
- L3 router plugin enabled
- Database access for neutron user
- Administrative privileges on neutron controller nodes

Step 1: Enable the Plugin
--------------------------

Edit the neutron configuration file (``/etc/neutron/neutron.conf``) and add the floating IP blocking plugin to the service plugins list:

.. code-block:: ini

   [DEFAULT]
   service_plugins = router,floating_ip_blocking

Step 2: Configure Plugin Options
---------------------------------

Add the floating IP blocking configuration section to ``/etc/neutron/neutron.conf``:

.. code-block:: ini

   [floating_ip_blocking]
   enabled = True
   max_rules_per_fip = 100
   auto_cleanup_empty_tables = True
   enable_conflict_detection = True

Configuration Options Reference
-------------------------------

The following configuration options are available for the floating IP blocking plugin:

.. code-block:: ini

   [floating_ip_blocking]
   # Enable or disable the floating IP blocking service
   # Default: True
   enabled = True
   
   # Maximum number of blocking rules per floating IP
   # Default: 100
   max_rules_per_fip = 100
   
   # Enable automatic cleanup of empty blocking tables
   # Default: True
   auto_cleanup_empty_tables = True
   
   # Enable rule conflict detection
   # Default: True
   enable_conflict_detection = True
   
   # Default rule priority for new rules
   # Default: 1000
   default_rule_priority = 1000
   
   # Enable audit logging for rule operations
   # Default: False
   enable_audit_logging = False
   
   # Audit log file path (only used if enable_audit_logging is True)
   # Default: /var/log/neutron/floating_ip_blocking_audit.log
   audit_log_file = /var/log/neutron/floating_ip_blocking_audit.log

Step 3: Apply Database Migration
---------------------------------

Run the database migration to create the required tables:

.. code-block:: bash

   neutron-db-manage --config-file /etc/neutron/neutron.conf upgrade head

Step 4: Restart Neutron Services
---------------------------------

Restart the neutron server and L3 agent services:

.. code-block:: bash

   # On the controller node
   systemctl restart neutron-server
   
   # On compute/network nodes
   systemctl restart neutron-l3-agent

Step 5: Verify Installation
---------------------------

Verify that the plugin is loaded correctly by checking the neutron server logs:

.. code-block:: bash

   grep "floating_ip_blocking" /var/log/neutron/server.log

Configuration Examples
======================

Basic Configuration
-------------------

For a basic setup with default settings:

.. code-block:: ini

   [DEFAULT]
   service_plugins = router,floating_ip_blocking
   
   [floating_ip_blocking]
   enabled = True

Production Configuration
------------------------

For a production environment with enhanced features:

.. code-block:: ini

   [DEFAULT]
   service_plugins = router,floating_ip_blocking
   
   [floating_ip_blocking]
   enabled = True
   max_rules_per_fip = 200
   auto_cleanup_empty_tables = True
   enable_conflict_detection = True
   enable_audit_logging = True
   audit_log_file = /var/log/neutron/floating_ip_blocking_audit.log

High-Performance Configuration
------------------------------

For high-performance environments with many floating IPs:

.. code-block:: ini

   [DEFAULT]
   service_plugins = router,floating_ip_blocking
   
   [floating_ip_blocking]
   enabled = True
   max_rules_per_fip = 500
   auto_cleanup_empty_tables = True
   enable_conflict_detection = False  # Disable for performance
   enable_performance_monitoring = True

API Usage Guide
===============

Authentication
--------------

All API calls require authentication. Include your authentication token in the request headers:

.. code-block:: bash

   export OS_TOKEN="your-auth-token"
   export NEUTRON_URL="http://controller:9696"
   export FIP_ID="2f245a7b-796b-4f26-9cf9-9e82d248fda7"

Seven Blocking Modes
---------------------

Mode 1: 1-tuple (Block All Traffic to Floating IP)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Block all ingress traffic to the floating IP:

.. code-block:: bash

   curl -X POST $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules \
     -H "Content-Type: application/json" \
     -H "X-Auth-Token: $OS_TOKEN" \
     -d '{
       "blocking_rule": {
         "direction": "ingress",
         "description": "Block all ingress traffic to this floating IP"
       }
     }'

Mode 2: 2-tuple (Block Traffic from Specific IP/Subnet)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Block all traffic from a specific subnet:

.. code-block:: bash

   curl -X POST $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules \
     -H "Content-Type: application/json" \
     -H "X-Auth-Token: $OS_TOKEN" \
     -d '{
       "blocking_rule": {
         "remote_ip": "***********/24",
         "direction": "ingress",
         "description": "Block all traffic from specific subnet"
       }
     }'

Mode 3A: 3-tuple (Block Specific Protocol + Local Port)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Block HTTP traffic to the floating IP:

.. code-block:: bash

   curl -X POST $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules \
     -H "Content-Type: application/json" \
     -H "X-Auth-Token: $OS_TOKEN" \
     -d '{
       "blocking_rule": {
         "protocol": "tcp",
         "local_port": 80,
         "direction": "ingress",
         "description": "Block HTTP traffic to this floating IP"
       }
     }'

Mode 5: 5-tuple (Complete Specification)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

Block specific HTTPS connection:

.. code-block:: bash

   curl -X POST $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules \
     -H "Content-Type: application/json" \
     -H "X-Auth-Token: $OS_TOKEN" \
     -d '{
       "blocking_rule": {
         "protocol": "tcp",
         "local_port": 443,
         "remote_ip": "***********/24",
         "remote_port": 8443,
         "direction": "ingress",
         "description": "Block specific HTTPS connection"
       }
     }'

Common Use Cases
----------------

Web Server Protection
~~~~~~~~~~~~~~~~~~~~~

Block common attack vectors for a web server:

.. code-block:: bash

   # Block SSH from internet
   curl -X POST $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules \
     -H "Content-Type: application/json" \
     -H "X-Auth-Token: $OS_TOKEN" \
     -d '{
       "blocking_rule": {
         "protocol": "tcp",
         "local_port": 22,
         "remote_ip": "0.0.0.0/0",
         "direction": "ingress",
         "description": "Block SSH from internet"
       }
     }'

Database Server Protection
~~~~~~~~~~~~~~~~~~~~~~~~~~

Protect a database server with specific access controls:

.. code-block:: bash

   # Block direct database access from internet
   curl -X POST $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules \
     -H "Content-Type: application/json" \
     -H "X-Auth-Token: $OS_TOKEN" \
     -d '{
       "blocking_rule": {
         "protocol": "tcp",
         "local_port": 3306,
         "direction": "ingress",
         "description": "Block direct MySQL access"
       }
     }'

Management Operations
---------------------

List All Blocking Rules
~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X GET $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules \
     -H "X-Auth-Token: $OS_TOKEN"

Update Blocking Rule
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   export RULE_ID="f47ac10b-58cc-4372-a567-0e02b2c3d479"
   curl -X PUT $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules/$RULE_ID \
     -H "Content-Type: application/json" \
     -H "X-Auth-Token: $OS_TOKEN" \
     -d '{
       "blocking_rule": {
         "protocol": "tcp",
         "local_port": 8080,
         "remote_ip": "***********/24",
         "direction": "ingress",
         "description": "Updated: Block HTTP access from entire subnet"
       }
     }'

Delete Blocking Rule
~~~~~~~~~~~~~~~~~~~

.. code-block:: bash

   curl -X DELETE $NEUTRON_URL/v2.0/floatingips/$FIP_ID/blocking-rules/$RULE_ID \
     -H "X-Auth-Token: $OS_TOKEN"

Python SDK Examples
-------------------

Using Requests Library
~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: python

   import requests
   import json

   def create_blocking_rule(token, neutron_url, fip_id, rule_data):
       headers = {
           'Content-Type': 'application/json',
           'X-Auth-Token': token
       }
       
       url = f"{neutron_url}/v2.0/floatingips/{fip_id}/blocking-rules"
       payload = {'blocking_rule': rule_data}
       
       response = requests.post(url, headers=headers, json=payload)
       
       if response.status_code == 201:
           return response.json()
       else:
           raise Exception(f"Failed to create rule: {response.text}")

   # Usage
   rule = create_blocking_rule(
       token="your-token",
       neutron_url="http://controller:9696",
       fip_id="2f245a7b-796b-4f26-9cf9-9e82d248fda7",
       rule_data={
           'protocol': 'tcp',
           'local_port': 22,
           'direction': 'ingress',
           'description': 'Block SSH access'
       }
   )

Troubleshooting
===============

Common Issues
-------------

Plugin Not Loading
~~~~~~~~~~~~~~~~~~

**Symptom:** Plugin does not appear in the service plugins list.

**Solution:**

1. Check that the plugin is correctly specified in ``service_plugins``
2. Verify that the neutron server has been restarted
3. Check the neutron server logs for any import errors

Database Migration Errors
~~~~~~~~~~~~~~~~~~~~~~~~~

**Symptom:** Database migration fails with permission errors.

**Solution:**

1. Ensure the neutron database user has CREATE and ALTER privileges
2. Check database connectivity
3. Verify that the neutron database exists

Rules Not Being Applied
~~~~~~~~~~~~~~~~~~~~~~

**Symptom:** Blocking rules are created but traffic is not blocked.

**Solution:**

1. Check that the L3 agent is running and connected
2. Verify that the floating IP is associated with a port
3. Check the L3 agent logs for synchronization errors
4. Verify iptables rules in the router namespace

Performance Issues
~~~~~~~~~~~~~~~~~~

**Symptom:** Slow API responses or high database load.

**Solution:**

1. Reduce ``max_rules_per_fip`` if you have many rules
2. Disable conflict detection for better performance
3. Enable rule caching with appropriate TTL

Error Handling
--------------

The API returns standard HTTP status codes and detailed error messages:

Floating IP Not Found (404)
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "error": {
       "type": "FloatingIPNotFound",
       "message": "Floating IP 2f245a7b-796b-4f26-9cf9-9e82d248fda7 not found",
       "code": 404
     }
   }

Rule Conflict (409)
~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
     "error": {
       "type": "BlockingRuleConflict",
       "message": "Blocking rule conflicts with existing rule 6ba7b810-9dad-11d1-80b4-00c04fd430c8",
       "code": 409,
       "details": {
         "conflict_type": "DUPLICATE",
         "existing_rule_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
         "suggestion": "Rule with identical parameters already exists"
       }
     }
   }

Monitoring and Maintenance
==========================

Log Monitoring
--------------

Monitor the following log files for issues:

- ``/var/log/neutron/server.log`` - Neutron server logs
- ``/var/log/neutron/l3-agent.log`` - L3 agent logs
- ``/var/log/neutron/floating_ip_blocking_audit.log`` - Audit logs (if enabled)

Performance Monitoring
----------------------

If performance monitoring is enabled, check the following metrics:

- Rule creation/deletion times
- Database query performance
- Agent synchronization latency
- Memory usage per rule

Regular Maintenance
-------------------

Perform the following maintenance tasks regularly:

1. **Clean up unused rules** - Remove rules that are no longer needed
2. **Review audit logs** - Check for any suspicious activity
3. **Monitor database growth** - Ensure adequate storage space
4. **Update documentation** - Keep rule descriptions current

Best Practices
==============

Rule Design
-----------

1. **Use specific rules** rather than broad rules when possible
2. **Document rule purposes** using the description field
3. **Test rules** in a development environment first
4. **Monitor rule effectiveness** and adjust as needed

Performance
-----------

1. **Limit the number of rules** per floating IP (recommended: < 50)
2. **Use CIDR notation** for IP ranges rather than individual IP rules
3. **Avoid overlapping rules** that could cause conflicts
4. **Clean up unused rules** regularly

Security
--------

1. **Follow the principle of least privilege**
2. **Regularly review and audit rules**
3. **Use automation** for rule management in large deployments
4. **Monitor for rule conflicts** and resolve them promptly

Migration and Upgrade
=====================

Backup and Recovery
-------------------

Database Backup
~~~~~~~~~~~~~~~

Include the floating IP blocking tables in your regular neutron database backups:

.. code-block:: bash

   mysqldump neutron floatingip_blocking_tables floatingip_blocking_rules > fip_blocking_backup.sql

Configuration Backup
~~~~~~~~~~~~~~~~~~~~

Backup the neutron configuration files:

.. code-block:: bash

   cp /etc/neutron/neutron.conf /etc/neutron/neutron.conf.backup

Recovery Procedure
~~~~~~~~~~~~~~~~~

To recover from a failure:

1. Restore the database from backup
2. Restore configuration files
3. Restart neutron services
4. Verify that rules are properly synchronized to agents

Upgrading
---------

When upgrading the floating IP blocking plugin:

1. **Backup** the database and configuration
2. **Stop** neutron services
3. **Update** the plugin code
4. **Run** database migrations
5. **Update** configuration if needed
6. **Start** neutron services
7. **Verify** functionality

Security Considerations
=======================

Access Control
--------------

- Limit floating IP blocking rule management to authorized users
- Use RBAC policies to control access
- Enable audit logging for compliance

Network Security
----------------

- Regularly review blocking rules for effectiveness
- Monitor for rule conflicts or gaps
- Test rules in a development environment first

Data Protection
---------------

- Encrypt database connections
- Secure audit log files
- Implement proper backup encryption