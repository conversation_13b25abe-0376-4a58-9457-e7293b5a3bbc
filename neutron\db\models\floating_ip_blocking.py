#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import sqlalchemy as sa
from sqlalchemy import orm

from neutron_lib.db import model_base

from neutron.db import standard_attr
from neutron.db.models import l3


class FloatingIPBlockingTable(standard_attr.HasStandardAttributes,
                              model_base.BASEV2, model_base.HasId,
                              model_base.HasProject):
    """Represents a floating IP blocking table.
    
    This table manages the lifecycle of blocking rules for a specific
    floating IP. It has a one-to-one relationship with FloatingIP.
    """

    __tablename__ = 'floatingip_blocking_tables'

    # One-to-one relationship with FloatingIP
    floatingip_id = sa.Column(sa.String(36),
                              sa.ForeignKey('floatingips.id', ondelete='CASCADE'),
                              nullable=False, unique=True)

    # Table attributes
    enabled = sa.Column(sa.Boolean, nullable=False, default=True)
    description = sa.Column(sa.String(255), nullable=True)

    # Relationship to FloatingIP
    floatingip = orm.relationship(
        l3.FloatingIP,
        backref=orm.backref('blocking_table',
                            uselist=False,
                            cascade='all,delete-orphan'))

    # Table constraints
    __table_args__ = (
        sa.UniqueConstraint('floatingip_id',
                            name='uniq_floatingip_blocking_tables0floatingip_id'),
        model_base.BASEV2.__table_args__,
    )


class FloatingIPBlockingRule(standard_attr.HasStandardAttributes,
                             model_base.BASEV2, model_base.HasId):
    """Represents a floating IP blocking rule.
    
    This table stores the five-tuple blocking rules associated with
    a floating IP blocking table.
    """

    __tablename__ = 'floatingip_blocking_rules'

    # Foreign key to blocking table
    blocking_table_id = sa.Column(sa.String(36),
                                  sa.ForeignKey('floatingip_blocking_tables.id',
                                                ondelete='CASCADE'),
                                  nullable=False)

    # Five-tuple fields (local IP is derived from blocking_table.floatingip)
    protocol = sa.Column(sa.String(10), nullable=True)  # TCP/UDP/ICMP/ANY
    local_port = sa.Column(sa.Integer, nullable=True)   # Optional local port
    remote_ip = sa.Column(sa.String(64), nullable=True)  # Optional remote IP (supports CIDR)
    remote_port = sa.Column(sa.Integer, nullable=True)  # Optional remote port

    # Traffic direction
    direction = sa.Column(sa.Enum('ingress', 'egress', name='traffic_direction'),
                          nullable=False)

    # Rule state
    enabled = sa.Column(sa.Boolean, nullable=False, default=True)

    # Rule metadata
    description = sa.Column(sa.String(255), nullable=True)

    # Relationship to blocking table
    blocking_table = orm.relationship(
        FloatingIPBlockingTable,
        backref=orm.backref('rules',
                            cascade='all,delete-orphan'))

    # Table constraints - ensure unique rules per blocking table
    __table_args__ = (
        sa.UniqueConstraint('blocking_table_id', 'protocol', 'local_port',
                            'remote_ip', 'remote_port', 'direction',
                            name='uniq_floatingip_blocking_rule0'
                                 'blocking_table_id0protocol0local_port0'
                                 'remote_ip0remote_port0direction'),
        model_base.BASEV2.__table_args__,
    )