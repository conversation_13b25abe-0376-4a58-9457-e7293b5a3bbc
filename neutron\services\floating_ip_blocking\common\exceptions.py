#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import functools
import time

from neutron._i18n import _
from neutron_lib import exceptions as n_exc
from oslo_db import exception as db_exc
from oslo_log import log as logging

LOG = logging.getLogger(__name__)


class FloatingIPBlockingException(n_exc.NeutronException):
    """浮动IP阻断系统基础异常"""
    pass


class BlockingTableNotFound(FloatingIPBlockingException):
    message = _("Blocking table for floating IP %(fip_id)s not found")


class BlockingTableAlreadyExists(FloatingIPBlockingException):
    message = _("Blocking table for floating IP %(fip_id)s already exists")


class BlockingRuleNotFound(FloatingIPBlockingException):
    message = _("Blocking rule %(rule_id)s not found")


class BlockingRuleConflict(FloatingIPBlockingException):
    message = _("Blocking rule conflicts with existing rule %(conflict_rule_id)s. "
               "Conflict type: %(conflict_type)s. "
               "Suggestion: %(suggestion)s")


class InvalidBlockingRule(FloatingIPBlockingException):
    message = _("Invalid blocking rule: %(reason)s")


class FloatingIPNotFound(FloatingIPBlockingException):
    message = _("Floating IP %(fip_id)s not found")


class BlockingRuleQuotaExceeded(FloatingIPBlockingException):
    message = _("Quota exceeded for blocking rules in table %(table_id)s. "
               "Current: %(current)s, Limit: %(limit)s")


class FailedToCreateOrUpdateBlockingRule(FloatingIPBlockingException):
    message = _("Failed to create or update blocking rule, error: %(error)s")


class BlockingRuleValidationError(FloatingIPBlockingException):
    message = _("Blocking rule validation failed: %(reason)s")


class BlockingTableOperationFailed(FloatingIPBlockingException):
    message = _("Blocking table operation failed for floating IP %(fip_id)s: %(reason)s")


class DatabaseOperationFailed(FloatingIPBlockingException):
    message = _("Database operation failed: %(operation)s. Error: %(error)s")


class AgentSyncFailed(FloatingIPBlockingException):
    message = _("Failed to sync blocking rules to agent %(agent_id)s: %(error)s")


class RuleConflictResolutionFailed(FloatingIPBlockingException):
    message = _("Failed to resolve rule conflicts: %(reason)s")


class BlockingServiceUnavailable(FloatingIPBlockingException):
    message = _("Floating IP blocking service is temporarily unavailable: %(reason)s")


class RuleConversionFailed(FloatingIPBlockingException):
    message = _("Failed to convert rule %(rule_id)s to %(target_format)s format: %(error)s")


# HTTP Status Code Mapping for API Layer
EXCEPTION_HTTP_STATUS_MAP = {
    BlockingTableNotFound: 404,
    BlockingRuleNotFound: 404,
    FloatingIPNotFound: 404,
    BlockingTableAlreadyExists: 409,
    BlockingRuleConflict: 409,
    InvalidBlockingRule: 400,
    BlockingRuleValidationError: 400,
    BlockingRuleQuotaExceeded: 413,  # Request Entity Too Large
    FailedToCreateOrUpdateBlockingRule: 500,
    BlockingTableOperationFailed: 500,
    DatabaseOperationFailed: 500,
    AgentSyncFailed: 503,  # Service Unavailable
    RuleConflictResolutionFailed: 422,  # Unprocessable Entity
    BlockingServiceUnavailable: 503,
    RuleConversionFailed: 500,
}


def get_http_status_code(exception):
    """Get HTTP status code for a given exception."""
    return EXCEPTION_HTTP_STATUS_MAP.get(type(exception), 500)


def format_error_response(exception, include_details=False):
    """Format exception into a standardized error response."""
    error_response = {
        'error': {
            'type': exception.__class__.__name__,
            'message': str(exception),
            'code': get_http_status_code(exception)
        }
    }
    
    if include_details and hasattr(exception, 'details'):
        error_response['error']['details'] = exception.details
        
    return error_response


def retry_on_db_error(max_retries=3, delay=1.0, backoff=2.0):
    """Decorator to retry database operations on transient failures."""
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_retries + 1):
                try:
                    return func(*args, **kwargs)
                except (db_exc.DBDeadlock, db_exc.DBConnectionError, 
                        db_exc.DBDuplicateEntry) as e:
                    last_exception = e
                    if attempt == max_retries:
                        LOG.error("Database operation failed after %d attempts: %s",
                                 max_retries + 1, str(e))
                        raise DatabaseOperationFailed(
                            operation=func.__name__, error=str(e))
                    
                    wait_time = delay * (backoff ** attempt)
                    LOG.warning("Database operation failed (attempt %d/%d), "
                               "retrying in %.2f seconds: %s",
                               attempt + 1, max_retries + 1, wait_time, str(e))
                    time.sleep(wait_time)
                except Exception as e:
                    # Non-retryable exceptions
                    LOG.error("Non-retryable database error in %s: %s",
                             func.__name__, str(e))
                    raise
                    
            # This should never be reached, but just in case
            raise last_exception
            
        return wrapper
    return decorator


def log_exception_details(exception, context=None, **kwargs):
    """Log detailed exception information for debugging."""
    log_data = {
        'exception_type': exception.__class__.__name__,
        'exception_message': str(exception),
        'context': context,
    }
    log_data.update(kwargs)
    
    LOG.error("FloatingIP blocking exception occurred: %(exception_type)s - "
              "%(exception_message)s. Context: %(context)s. "
              "Additional data: %(additional)s",
              {
                  'exception_type': log_data['exception_type'],
                  'exception_message': log_data['exception_message'],
                  'context': log_data.get('context', 'N/A'),
                  'additional': {k: v for k, v in log_data.items() 
                               if k not in ['exception_type', 'exception_message', 'context']}
              })


def handle_database_errors(func):
    """Decorator to handle and convert database errors to appropriate exceptions."""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except db_exc.DBReferenceError as e:
            # Foreign key constraint violation
            if 'floatingip' in str(e).lower():
                raise FloatingIPNotFound(fip_id='unknown')
            raise DatabaseOperationFailed(operation=func.__name__, error=str(e))
        except db_exc.DBDuplicateEntry as e:
            # Unique constraint violation
            if 'floatingip_id' in str(e):
                raise BlockingTableAlreadyExists(fip_id='unknown')
            raise DatabaseOperationFailed(operation=func.__name__, error=str(e))
        except db_exc.DBError as e:
            # General database error
            raise DatabaseOperationFailed(operation=func.__name__, error=str(e))
        except Exception as e:
            # Unexpected error
            LOG.exception("Unexpected error in database operation %s", func.__name__)
            raise DatabaseOperationFailed(operation=func.__name__, error=str(e))
            
    return wrapper


class ConflictDetails:
    """Helper class to structure conflict details."""
    
    def __init__(self, conflict_type, existing_rule_id, suggestion):
        self.conflict_type = conflict_type
        self.existing_rule_id = existing_rule_id
        self.suggestion = suggestion
        
    def to_dict(self):
        return {
            'conflict_type': self.conflict_type,
            'existing_rule_id': self.existing_rule_id,
            'suggestion': self.suggestion
        }


def create_conflict_exception(conflicts):
    """Create a BlockingRuleConflict exception with detailed conflict information."""
    if not conflicts:
        return None
        
    # Use the first conflict for the main exception message
    first_conflict = conflicts[0]
    
    exception = BlockingRuleConflict(
        conflict_rule_id=first_conflict.get('rule_id', 'unknown'),
        conflict_type=first_conflict.get('conflict_type', 'unknown'),
        suggestion=first_conflict.get('resolution_suggestion', 'Review and adjust rule')
    )
    
    # Attach all conflicts as details
    exception.details = [ConflictDetails(
        conflict_type=c.get('conflict_type', 'unknown'),
        existing_rule_id=c.get('rule_id', 'unknown'),
        suggestion=c.get('resolution_suggestion', 'Review and adjust rule')
    ).to_dict() for c in conflicts]
    
    return exception


def validate_and_raise_if_invalid(validation_func, *args, **kwargs):
    """Helper function to validate and raise appropriate exceptions."""
    try:
        result = validation_func(*args, **kwargs)
        if result is False:
            raise BlockingRuleValidationError(
                reason="Validation function returned False")
        return result
    except FloatingIPBlockingException:
        # Re-raise our own exceptions
        raise
    except Exception as e:
        # Convert other exceptions to validation errors
        raise BlockingRuleValidationError(reason=str(e))