# Copyright (c) 2014 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc
import collections

import netaddr
from neutron_lib import constants as lib_constants
from neutron_lib.utils import helpers
from oslo_log import log as logging
from oslo_utils import excutils
import six

from neutron._i18n import _
from neutron.agent.l3 import namespaces
from neutron.agent.linux import ip_lib
from neutron.agent.linux import iptables_manager
from neutron.agent.linux import ra
from neutron.common import constants as n_const
from neutron.common import exceptions as n_exc
from neutron.common import ipv6_utils
from neutron.common import utils as common_utils
from neutron.ipam import utils as ipam_utils

LOG = logging.getLogger(__name__)
HA_DEV_PREFIX = 'ha-'
INTERNAL_DEV_PREFIX = namespaces.INTERNAL_DEV_PREFIX
EXTERNAL_DEV_PREFIX = namespaces.EXTERNAL_DEV_PREFIX

FLOATINGIP_STATUS_NOCHANGE = object()
ADDRESS_SCOPE_MARK_MASK = "0xffff0000"
ADDRESS_SCOPE_MARK_ID_MIN = 1024
ADDRESS_SCOPE_MARK_ID_MAX = 2048
DEFAULT_ADDRESS_SCOPE = "noscope"


@six.add_metaclass(abc.ABCMeta)
class BaseRouterInfo(object):

    def __init__(self,
                 agent,
                 router_id,
                 router,
                 agent_conf,
                 use_ipv6=False):
        self.agent = agent
        self.backend = agent.backend
        self.router_id = router_id
        self.ex_gw_port = None
        # Invoke the setter for establishing initial SNAT action
        self._snat_enabled = None
        self._snat66_enabled = None
        self.router = router
        self.agent_conf = agent_conf
        self.use_ipv6 = use_ipv6

        self.internal_ports = []
        self.ns_name = None
        self.process_monitor = None

    @property
    def router(self):
        return self._router

    @router.setter
    def router(self, value):
        self._router = value
        if not self._router:
            return
        # enable_snat by default if it wasn't specified by plugin
        self._snat_enabled = self._router.get('enable_snat', True)
        self._snat66_enabled = self._router.get('enable_snat66', False)

    @abc.abstractmethod
    def delete(self, agent):
        pass

    @abc.abstractmethod
    def process(self, agent):
        """Process updates to this router

        This method is the point where the agent requests that updates be
        applied to this router.

        :param agent: Passes the agent in order to send RPC messages.
        """
        pass

    def get_ex_gw_port(self):
        return self.router.get('gw_port')

    def get_gw_ns_name(self):
        return self.ns_name

    def get_internal_device_name(self, port_id):
        return (INTERNAL_DEV_PREFIX + port_id)[:self.driver.DEV_NAME_LEN]

    def get_external_device_name(self, port_id):
        return (EXTERNAL_DEV_PREFIX + port_id)[:self.driver.DEV_NAME_LEN]

    def get_external_device_interface_name(self, ex_gw_port):
        return self.get_external_device_name(ex_gw_port['id'])


class RouterInfo(BaseRouterInfo):

    def __init__(self,
                 agent,
                 router_id,
                 router,
                 agent_conf,
                 use_ipv6=False):
        super(RouterInfo, self).__init__(
            agent, router_id, router, agent_conf, use_ipv6)
        self.ex_gw_port = None
        self.fip_map = {}
        self.internal_ports = []
        self.pd_subnets = {}
        self.floating_ips = set()
        ns = self.create_router_namespace_object(
            router_id, agent_conf, self.backend.interface_driver, use_ipv6)
        self.router_namespace = ns
        self.ns_name = ns.name
        self.available_mark_ids = set(range(ADDRESS_SCOPE_MARK_ID_MIN,
                                            ADDRESS_SCOPE_MARK_ID_MAX))
        self._address_scope_to_mark_id = {
            DEFAULT_ADDRESS_SCOPE: self.available_mark_ids.pop()}
        self.iptables_manager = iptables_manager.IptablesManager(
            use_ipv6=use_ipv6,
            namespace=self.ns_name)
        self.initialize_address_scope_iptables()
        if self.agent_conf.wo_enable_fip_admin_state_up:
            self.initialize_address_fip_admin_iptables()
        if self.agent_conf.enable_wo_fip_ad_ports:
            self.initialize_fip_denied_ports_iptables()
        self.initialize_route_table_raw_iptables()
        self.initialize_metadata_iptables()
        self.routes = []
        self.driver = self.backend.interface_driver
        # radvd is a neutron.agent.linux.ra.DaemonMonitor
        self.radvd = None
        self.centralized_port_forwarding_fip_set = set()
        self.fip_managed_by_port_forwardings = None

        self.centralize_floatingip_esnat_ip_set = set()
        self.fip_managed_by_elastic_snats = None

    def initialize(self, light=False):
        radvd_dm = ra.DaemonMonitor(self.router_id,
                                    self.ns_name,
                                    self.backend.process_monitor,
                                    self.get_internal_device_name,
                                    self.agent_conf)

        if self.agent_conf.enable_radvd:
            self.radvd = radvd_dm
        else:
            # Disable existing radvd
            radvd_dm.disable()

        if not light:
            self.router_namespace.create()

    def create_router_namespace_object(
            self, router_id, agent_conf, iface_driver, use_ipv6):
        return namespaces.RouterNamespace(
            router_id, agent_conf, iface_driver, use_ipv6)

    @property
    def router(self):
        return self._router

    @router.setter
    def router(self, value):
        self._router = value
        if not self._router:
            return
        # enable_snat by default if it wasn't specified by plugin
        self._snat_enabled = self._router.get('enable_snat', True)

    def get_internal_device_name(self, port_id):
        return (INTERNAL_DEV_PREFIX + port_id)[:n_const.LINUX_DEV_LEN]

    def get_external_device_name(self, port_id):
        return (EXTERNAL_DEV_PREFIX + port_id)[:n_const.LINUX_DEV_LEN]

    def get_external_device_interface_name(self, ex_gw_port):
        return self.get_external_device_name(ex_gw_port['id'])

    def get_gw_ns_name(self):
        return self.ns_name

    def _update_routing_table(self, operation, route, namespace):
        cmd = ['ip', 'route', operation, 'to', route['destination'],
               'via', route['nexthop']]
        ip_wrapper = ip_lib.IPWrapper(namespace=namespace)
        try:
            ip_wrapper.netns.execute(cmd, log_fail_as_error=False)
        except RuntimeError as e:
            # Change the following errors to warnings:
            # 1. Network is unreachable: the nexthop is not directly connected
            # 2. No such process: when deleting a route that does not exist
            if any(s in e.message for s in
                   ['Network is unreachable', 'No such process',
                    'Nexthop has invalid gateway']):
                LOG.warning(e.message)
            else:
                LOG.error(e.message)

    def update_routing_table(self, operation, route):
        self._update_routing_table(operation, route, self.ns_name)

    def routes_updated(self, old_routes, new_routes):
        adds, removes = helpers.diff_list_of_dict(old_routes,
                                                  new_routes)
        for route in adds:
            LOG.debug("Added route entry is '%s'", route)
            # remove replaced route from deleted route
            for del_route in removes:
                if route['destination'] == del_route['destination']:
                    removes.remove(del_route)
            # replace success even if there is no existing route
            self.update_routing_table('replace', route)
        for route in removes:
            LOG.debug("Removed route entry is '%s'", route)
            self.update_routing_table('delete', route)

    def get_floating_ips(self):
        """Filter Floating IPs to be hosted on this agent."""
        return self.router.get(lib_constants.FLOATINGIP_KEY, [])

    def get_port_forwarding_fips(self):
        """Get router port forwarding floating IPs."""
        return self.router.get(n_const.PORT_FORWARDING_FLOATINGIP_KEY, [])

    def get_elastic_snat_fips(self):
        """Filter Floating IPs to be hosted on this agent."""
        return self.router.get('_elastic_snat_floatingips', [])

    def floating_forward_rules(self, fip):
        fixed_ip = fip['fixed_ip_address']
        floating_ip = fip['floating_ip_address']
        to_source = '-s %s/32 -j SNAT --to-source %s' % (fixed_ip, floating_ip)
        if self.iptables_manager.random_fully:
            to_source += ' --random-fully'
        return [('PREROUTING', '-d %s/32 -j DNAT --to-destination %s' %
                 (floating_ip, fixed_ip)),
                ('OUTPUT', '-d %s/32 -j DNAT --to-destination %s' %
                 (floating_ip, fixed_ip)),
                ('float-snat', to_source)]

    def floating_v6_forward_rules(self, fip):
        fixed_ip = fip['fixed_ip_address']
        floating_ip = fip['floating_ip_address']
        to_source = '-s %s/128 -j SNAT --to-source %s' \
                    % (fixed_ip, floating_ip)
        if self.iptables_manager.random_fully:
            to_source += ' --random-fully'
        return [('PREROUTING', '-d %s/128 -j DNAT --to-destination %s' %
                 (floating_ip, fixed_ip)),
                ('OUTPUT', '-d %s/128 -j DNAT --to-destination %s' %
                 (floating_ip, fixed_ip)),
                ('float-snat', to_source)]

    def floating_mangle_rules(self, floating_ip, fixed_ip, internal_mark):
        mark_traffic_to_floating_ip = (
            'floatingip', '-d %s/32 -j MARK --set-xmark %s' % (
                floating_ip, internal_mark))
        mark_traffic_from_fixed_ip = (
            'FORWARD', '-s %s/32 -j $float-snat' % fixed_ip)
        return [mark_traffic_to_floating_ip, mark_traffic_from_fixed_ip]

    def get_address_scope_mark_mask(self, address_scope=None):
        if not address_scope:
            address_scope = DEFAULT_ADDRESS_SCOPE

        if address_scope not in self._address_scope_to_mark_id:
            self._address_scope_to_mark_id[address_scope] = (
                self.available_mark_ids.pop())

        mark_id = self._address_scope_to_mark_id[address_scope]
        # NOTE: Address scopes use only the upper 16 bits of the 32 fwmark
        return "%s/%s" % (hex(mark_id << 16), ADDRESS_SCOPE_MARK_MASK)

    def get_port_address_scope_mark(self, port):
        """Get the IP version 4 and 6 address scope mark for the port

        :param port: A port dict from the RPC call
        :returns: A dict mapping the address family to the address scope mark
        """
        port_scopes = port.get('address_scopes', {})

        address_scope_mark_masks = (
            (int(k), self.get_address_scope_mark_mask(v))
            for k, v in port_scopes.items())
        return collections.defaultdict(self.get_address_scope_mark_mask,
                                       address_scope_mark_masks)

    def need_process_fip(self, fip):
        # Filter out 'gatewayip', 'ecs ipv6' and using port_forwarding fips
        if fip['fip_type'] != lib_constants.FLOATINGIP_TYPE_FIP or (
                fip['fip_type'] == lib_constants.FLOATINGIP_TYPE_FIP and
                not fip['fixed_ip_address']):
            return False
        return True

    def process_floating_ip_nat_rules(self):
        """Configure NAT rules for the router's floating IPs.

        Configures iptables rules for the floating ips of the given router
        """
        # Clear out all iptables rules for floating ips
        self.iptables_manager.ipv4['nat'].clear_rules_by_tag('floating_ip')
        if self.agent_conf.enable_nat6to6:
            self.iptables_manager.ipv6['nat'].clear_rules_by_tag('floating_ip')

        floating_ips = self.get_floating_ips()
        # Loop once to ensure that floating ips are configured.
        for fip in floating_ips:
            # Rebuild iptables rules for the floating ip.
            # Filter out 'gatewayip', 'ecs ipv6' and using port_forwarding fips

            if not self.need_process_fip(fip):
                continue

            fip_ip = fip['floating_ip_address']
            addr = netaddr.IPAddress(fip_ip)
            if addr.version == lib_constants.IP_VERSION_4:
                for chain, rule in self.floating_forward_rules(fip):
                    self.iptables_manager.ipv4['nat'].add_rule(
                        chain, rule, tag='floating_ip')
            elif self.agent_conf.enable_nat6to6:
                for chain, rule in self.floating_v6_forward_rules(fip):
                    self.iptables_manager.ipv6['nat'].add_rule(
                        chain, rule, tag='floating_ip')
        self.iptables_manager.apply()

    def _process_pd_iptables_rules(self, prefix, subnet_id):
        """Configure iptables rules for prefix delegated subnets"""
        ext_scope = self._get_external_address_scope()
        ext_scope_mark = self.get_address_scope_mark_mask(ext_scope)
        ex_gw_device = self.get_external_device_name(
            self.get_ex_gw_port()['id'])
        scope_rule = self.address_scope_mangle_rule(ex_gw_device,
                                                    ext_scope_mark)
        self.iptables_manager.ipv6['mangle'].add_rule(
            'scope',
            '-d %s ' % prefix + scope_rule,
            tag=('prefix_delegation_%s' % subnet_id))

    def process_floating_ip_address_scope_rules(self):
        """Configure address scope related iptables rules for the router's
         floating IPs.
        """

        # Clear out all iptables rules for floating ips
        self.iptables_manager.ipv4['mangle'].clear_rules_by_tag('floating_ip')
        if self.agent_conf.enable_nat6to6:
            self.iptables_manager.ipv6['mangle'].clear_rules_by_tag(
                'floating_ip')
        all_floating_ips = self.get_floating_ips()
        ext_scope = self._get_external_address_scope()
        # Filter out the floating ips that have fixed ip in the same address
        # scope. Because the packets for them will always be in one address
        # scope, no need to manipulate MARK/CONNMARK for them.
        floating_ips = [fip for fip in all_floating_ips
                        if fip.get('fixed_ip_address_scope') != ext_scope]
        if floating_ips:
            ext_scope_mark = self.get_address_scope_mark_mask(ext_scope)
            ports_scopemark = self._get_address_scope_mark()
            devices_in_ext_scope = {
                device for device, mark
                in ports_scopemark[lib_constants.IP_VERSION_4].items()
                if mark == ext_scope_mark}
            # Add address scope for floatingip egress
            for device in devices_in_ext_scope:
                self.iptables_manager.ipv4['mangle'].add_rule(
                    'float-snat',
                    '-o %s -j MARK --set-xmark %s'
                    % (device, ext_scope_mark),
                    tag='floating_ip')
                if self.agent_conf.enable_nat6to6:
                    self.iptables_manager.ipv6['mangle'].add_rule(
                        'float-snat',
                        '-o %s -j MARK --set-xmark %s'
                        % (device, ext_scope_mark),
                        tag='floating_ip')

        # Loop once to ensure that floating ips are configured.
        for fip in floating_ips:
            # Rebuild iptables rules for the floating ip.
            fip_ip = fip['floating_ip_address']
            addr = netaddr.IPAddress(fip_ip)
            # Send the floating ip traffic to the right address scope
            fixed_ip = fip['fixed_ip_address']

            # Filter out 'gatewayip', 'ecs ipv6'
            if not self.need_process_fip(fip):
                continue

            fixed_scope = fip.get('fixed_ip_address_scope')
            internal_mark = self.get_address_scope_mark_mask(fixed_scope)
            mangle_rules = self.floating_mangle_rules(
                fip_ip, fixed_ip, internal_mark)
            for chain, rule in mangle_rules:
                if addr.version == lib_constants.IP_VERSION_4:
                    self.iptables_manager.ipv4['mangle'].add_rule(
                        chain, rule, tag='floating_ip')
                elif self.agent_conf.enable_nat6to6:
                    self.iptables_manager.ipv6['mangle'].add_rule(
                        chain, rule, tag='floating_ip')

    def process_floating_ip_address_fip_admin_rules(self):
        self.iptables_manager.ipv4['raw'].empty_chain('fip-admin')
        if self.agent_conf.enable_nat6to6:
            self.iptables_manager.ipv6['raw'].empty_chain('fip-admin')
        self.iptables_manager.ipv4['filter'].empty_chain('fip-admin')
        self.iptables_manager.ipv6['filter'].empty_chain('fip-admin')
        ex_gw_port = self.get_ex_gw_port()
        if not ex_gw_port:
            return
        device_name = self.get_external_device_interface_name(ex_gw_port)
        floating_ips = (
                self.get_floating_ips() + self.get_port_forwarding_fips() +
                self.get_elastic_snat_fips())
        for fip in floating_ips:
            admin_state_up = fip.get('admin_state_up', True)
            if admin_state_up:
                continue
            floating_ip_type = fip.get('fip_type', 'floatingip')
            floating_ip_addr = fip['floating_ip_address']
            fixed_ip_address = fip['fixed_ip_address']
            if floating_ip_type == lib_constants.FLOATINGIP_TYPE_FIP:
                if netaddr.IPAddress(floating_ip_addr).version == 4:
                    ingress_rule = ('-d %s/32 -j DROP' % floating_ip_addr)
                    self.iptables_manager.ipv4['raw'].add_rule(
                        'fip-admin', ingress_rule, tag='floatingip_admin')
                    if not fixed_ip_address:
                        continue
                    egress_rule = ('-o %s -s %s/32 -j DROP' % (
                        device_name, fixed_ip_address))
                    self.iptables_manager.ipv4['filter'].add_rule(
                        'fip-admin', egress_rule, tag='floatingip_admin')
                elif netaddr.IPAddress(floating_ip_addr).version == 6 and (
                        self.agent_conf.enable_nat6to6):
                    ingress_rule = ('-d %s/128 -j DROP' % floating_ip_addr)
                    self.iptables_manager.ipv6['raw'].add_rule(
                        'fip-admin', ingress_rule, tag='floatingip_admin')
                    if not fixed_ip_address:
                        continue
                    egress_rule = ('-o %s -s %s/128 -j DROP' % (
                        device_name, fixed_ip_address))
                    self.iptables_manager.ipv6['filter'].add_rule(
                        'fip-admin', egress_rule, tag='floatingip_admin')
            elif floating_ip_type == lib_constants.FLOATINGIP_TYPE_ECS_IPv6:
                if self.agent_conf.enable_nat6to6:
                    continue
                fip_admin_rules = self.ipv6_fip_admin_filter_rule(
                    device_name, floating_ip_addr)
                for rule in fip_admin_rules:
                    self.iptables_manager.ipv6['filter'].add_rule(
                        'fip-admin', rule, tag='floatingip_admin')

    def process_fip_denied_ports_rules(self):
        self.iptables_manager.ipv4['raw'].empty_chain('denied-port')
        if self.agent_conf.enable_nat6to6:
            self.iptables_manager.ipv6['raw'].empty_chain('denied-port')
        else:
            self.iptables_manager.ipv6['filter'].empty_chain('denied-port')
        ex_gw_port = self.get_ex_gw_port()
        if not ex_gw_port:
            return

        # For floating IPs belonging to the router, regardless of
        # whether there is traffic access, denied_ports rules are
        # processed to avoid the problem of not adding denied_ports
        # rules when snat and dnat are created simultaneously.
        floating_ips = (
                self.get_floating_ips() + self.get_port_forwarding_fips() +
                self.get_elastic_snat_fips())
        device_name = self.get_external_device_interface_name(ex_gw_port)
        for fip in floating_ips:
            fip_address = fip['floating_ip_address']
            fip_type = fip.get('fip_type', 'floatingip')
            fip_denied_ports_list = fip.get('denied_port_numbers', [])
            if not fip_denied_ports_list:
                continue
            if fip_type != lib_constants.FLOATINGIP_TYPE_ECS_IPv6:
                for port_num in fip_denied_ports_list:
                    if netaddr.IPAddress(fip_address).version == 4:
                        rules = self.port_num_ipv4_rule(
                            device_name, fip_address, port_num)
                        for rule in rules:
                            self.iptables_manager.ipv4['raw'].add_rule(
                                'denied-port', rule, tag='denied-port')
                    if netaddr.IPAddress(fip_address).version == 6 and (
                            self.agent_conf.enable_nat6to6):
                        rules = self.port_num_ipv6_rule(
                            device_name, fip_address, port_num)
                        for rule in rules:
                            self.iptables_manager.ipv6['raw'].add_rule(
                                'denied-port', rule, tag='denied-port')
            else:
                if self.agent_conf.enable_nat6to6:
                    continue
                admin_state_up = fip.get('admin_state_up', False)
                if not admin_state_up:
                    continue
                for port_num in fip_denied_ports_list:
                    rules = self.port_num_ipv6_rule(
                        device_name, fip_address, port_num)
                    for rule in rules:
                        self.iptables_manager.ipv6['filter'].add_rule(
                            'denied-port', rule, tag='denied-port')

    def process_snat_dnat_for_fip(self):
        try:
            self.process_floating_ip_nat_rules()
        except Exception:
            # TODO(salv-orlando): Less broad catching
            msg = _('L3 agent failure to setup NAT for floating IPs')
            LOG.exception(msg)
            raise n_exc.FloatingIpSetupException(msg)

    def _add_fip_addr_to_device(self, fip, device):
        """Configures the floating ip address on the device.
        """
        try:
            ip_cidr = common_utils.ip_to_cidr(fip['floating_ip_address'])
            device.addr.add(ip_cidr)
            return True
        except RuntimeError:
            # any exception occurred here should cause the floating IP
            # to be set in error state
            LOG.warning("Unable to configure IP address for "
                        "floating IP: %s", fip['id'])

    def add_floating_ip(self, fip, interface_name, device):
        raise NotImplementedError()

    def migrate_centralized_floating_ip(self, fip, interface_name, device):
        pass

    def gateway_redirect_cleanup(self, rtr_interface):
        pass

    def remove_floating_ip(self, device, ip_cidr):
        device.delete_addr_and_conntrack_state(ip_cidr)

    def move_floating_ip(self, fip):
        return lib_constants.FLOATINGIP_STATUS_ACTIVE

    def remove_external_gateway_ip(self, device, ip_cidr):
        device.delete_addr_and_conntrack_state(ip_cidr)

    def get_router_cidrs(self, device):
        return set([addr['cidr'] for addr in device.addr.list()])

    def get_centralized_fip_cidr_set(self):
        return set()

    def process_floating_ip_addresses(self, interface_name):
        """Configure IP addresses on router's external gateway interface.

        Ensures addresses for existing floating IPs and cleans up
        those that should not longer be configured.
        """

        fip_statuses = {}
        if interface_name is None:
            LOG.debug('No Interface for floating IPs router: %s',
                      self.router['id'])
            return fip_statuses

        device = ip_lib.IPDevice(interface_name, namespace=self.ns_name)
        existing_cidrs = self.get_router_cidrs(device)
        new_cidrs = set()
        gw_cidrs = self._get_gw_ips_cidr()
        centralized_fip_cidrs = self.get_centralized_fip_cidr_set()
        floating_ips = self.get_floating_ips()
        # Loop once to ensure that floating ips are configured.
        for fip in floating_ips:
            fip_statuses[fip['id']] = lib_constants.FLOATINGIP_STATUS_ACTIVE
            # Filter out 'gatewayip', 'ecs ipv6' and using port_forwarding fips
            if fip['fip_type'] != lib_constants.FLOATINGIP_TYPE_FIP or (
                    fip['fip_type'] == lib_constants.FLOATINGIP_TYPE_FIP and
                    not fip.get('fixed_ip_address')):
                continue
            fip_ip = fip['floating_ip_address']
            ip_cidr = common_utils.ip_to_cidr(fip_ip)
            new_cidrs.add(ip_cidr)

            if ip_cidr not in existing_cidrs:
                fip_statuses[fip['id']] = self.add_floating_ip(
                    fip, interface_name, device)
                LOG.debug('Floating ip %(id)s added, status %(status)s',
                          {'id': fip['id'],
                           'status': fip_statuses.get(fip['id'])})
            elif (fip_ip in self.fip_map and
                  self.fip_map[fip_ip] != fip['fixed_ip_address']):
                LOG.debug("Floating IP was moved from fixed IP "
                          "%(old)s to %(new)s",
                          {'old': self.fip_map[fip_ip],
                           'new': fip['fixed_ip_address']})
                fip_statuses[fip['id']] = self.move_floating_ip(fip)
            elif (ip_cidr in centralized_fip_cidrs and
                fip.get('host') == self.host):
                LOG.debug("Floating IP is migrating from centralized "
                          "to distributed: %s", fip)
                fip_statuses[fip['id']] = self.migrate_centralized_floating_ip(
                    fip, interface_name, device)
            elif fip_statuses[fip['id']] == fip['status']:
                # mark the status as not changed. we can't remove it because
                # that's how the caller determines that it was removed
                fip_statuses[fip['id']] = FLOATINGIP_STATUS_NOCHANGE
        fips_to_remove = (
            ip_cidr
            for ip_cidr in (existing_cidrs - new_cidrs - gw_cidrs -
                            self.centralized_port_forwarding_fip_set -
                            self.centralize_floatingip_esnat_ip_set)
            if common_utils.is_cidr_host(ip_cidr))
        for ip_cidr in fips_to_remove:
            LOG.debug("Removing floating ip %s from interface %s in "
                      "namespace %s", ip_cidr, interface_name, self.ns_name)
            self.remove_floating_ip(device, ip_cidr)

        return fip_statuses

    def _get_gw_ips_cidr(self):
        gw_cidrs = set()
        ex_gw_port = self.get_ex_gw_port()
        if ex_gw_port:
            for ip_addr in ex_gw_port['fixed_ips']:
                ex_gw_ip = ip_addr['ip_address']
                addr = netaddr.IPAddress(ex_gw_ip)
                if addr.version == lib_constants.IP_VERSION_4:
                    gw_cidrs.add(common_utils.ip_to_cidr(ex_gw_ip))
        return gw_cidrs

    def configure_fip_addresses(self, interface_name):
        try:
            return self.process_floating_ip_addresses(interface_name)
        except Exception:
            # TODO(salv-orlando): Less broad catching
            msg = _('L3 agent failure to setup floating IPs')
            LOG.exception(msg)
            raise n_exc.FloatingIpSetupException(msg)

    def put_fips_in_error_state(self):
        fip_statuses = {}
        for fip in self.router.get(lib_constants.FLOATINGIP_KEY, []):
            fip_statuses[fip['id']] = lib_constants.FLOATINGIP_STATUS_ERROR
        return fip_statuses

    def delete(self):
        self.router['gw_port'] = None
        self.router[lib_constants.INTERFACE_KEY] = []
        self.router[lib_constants.FLOATINGIP_KEY] = []
        self.process_delete()
        self.disable_radvd()
        self.router_namespace.delete()

    def _internal_network_updated(self, port, subnet_id, prefix, old_prefix,
                                  updated_cidrs):
        interface_name = self.get_internal_device_name(port['id'])
        if prefix != lib_constants.PROVISIONAL_IPV6_PD_PREFIX:
            fixed_ips = port['fixed_ips']
            for fixed_ip in fixed_ips:
                if fixed_ip['subnet_id'] == subnet_id:
                    v6addr = common_utils.ip_to_cidr(fixed_ip['ip_address'],
                                                     fixed_ip.get('prefixlen'))
                    if v6addr not in updated_cidrs:
                        self.driver.add_ipv6_addr(
                            interface_name, v6addr, self.ns_name)
        else:
            self.driver.delete_ipv6_addr_with_prefix(
                interface_name, old_prefix, self.ns_name)

    def _internal_network_added(self, ns_name, network_id, port_id,
                                fixed_ips, mac_address,
                                interface_name, prefix, mtu=None):
        LOG.debug("adding internal network: prefix(%s), port(%s)",
                  prefix, port_id)
        self.driver.plug(
            network_id, port_id, interface_name,
            mac_address, namespace=ns_name, prefix=prefix, mtu=mtu)

        ip_cidrs = common_utils.fixed_ip_cidrs(fixed_ips)
        self.driver.init_router_port(
            interface_name, ip_cidrs, namespace=ns_name)
        for fixed_ip in fixed_ips:
            ip_lib.send_ip_addr_adv_notif(ns_name,
                                          interface_name,
                                          fixed_ip['ip_address'])

    def internal_network_added(self, port):
        network_id = port['network_id']
        port_id = port['id']
        fixed_ips = port['fixed_ips']
        mac_address = port['mac_address']

        interface_name = self.get_internal_device_name(port_id)

        self._internal_network_added(self.ns_name,
                                     network_id,
                                     port_id,
                                     fixed_ips,
                                     mac_address,
                                     interface_name,
                                     INTERNAL_DEV_PREFIX,
                                     mtu=port.get('mtu'))

    def internal_network_removed(self, port):
        interface_name = self.get_internal_device_name(port['id'])
        LOG.debug("removing internal network: port(%s) interface(%s)",
                  port['id'], interface_name)
        if ip_lib.device_exists(interface_name, namespace=self.ns_name):
            self.driver.unplug(
                interface_name, namespace=self.ns_name,
                prefix=INTERNAL_DEV_PREFIX)

    def _get_existing_devices(self):
        ip_wrapper = ip_lib.IPWrapper(namespace=self.ns_name)
        ip_devs = ip_wrapper.get_devices()
        return [ip_dev.name for ip_dev in ip_devs]

    def _update_internal_ports_cache(self, port):
        # NOTE(slaweq): self.internal_ports is a list of port objects but
        # when it is updated in _process_internal_ports() method,
        # but it can be based only on indexes of elements in
        # self.internal_ports as index of element to updated is unknown.
        # It has to be done based on port_id and this method is doing exactly
        # that.
        for index, p in enumerate(self.internal_ports):
            if p['id'] == port['id']:
                self.internal_ports[index] = port
                break
        else:
            self.internal_ports.append(port)

    @staticmethod
    def _get_updated_ports(existing_ports, current_ports):
        updated_ports = []
        current_ports_dict = {p['id']: p for p in current_ports}
        for existing_port in existing_ports:
            if not existing_port.get('fixed_ips'):
                LOG.warning("Port %s doesn't have ip address",
                            existing_port['id'])
                continue
            current_port = current_ports_dict.get(existing_port['id'])
            if current_port:
                fixed_ips_changed = (
                    sorted(existing_port['fixed_ips'],
                           key=helpers.safe_sort_key) !=
                    sorted(current_port['fixed_ips'],
                           key=helpers.safe_sort_key))
                mtu_changed = existing_port['mtu'] != current_port['mtu']
                if fixed_ips_changed or mtu_changed:
                    updated_ports.append(current_port)
        return updated_ports

    @staticmethod
    def _port_has_ipv6_subnet(port):
        if 'subnets' in port:
            for subnet in port['subnets']:
                if (netaddr.IPNetwork(subnet['cidr']).version == 6 and
                    subnet['cidr'] !=
                        lib_constants.PROVISIONAL_IPV6_PD_PREFIX):
                    return True

    def enable_radvd(self, internal_ports=None):
        if not self.radvd:
            return
        LOG.debug('Spawning radvd daemon in router device: %s', self.router_id)
        if not internal_ports:
            internal_ports = self.internal_ports
        self.radvd.enable(internal_ports)

    def disable_radvd(self):
        if not self.radvd:
            return
        LOG.debug('Terminating radvd daemon in router device: %s',
                  self.router_id)
        self.radvd.disable()

    def internal_network_updated(self, interface_name, ip_cidrs, mtu):
        self.driver.set_mtu(
            interface_name, mtu, namespace=self.ns_name,
            prefix=INTERNAL_DEV_PREFIX)
        self.driver.init_router_port(
            interface_name,
            ip_cidrs=ip_cidrs,
            namespace=self.ns_name)

    def address_scope_mangle_rule(self, device_name, mark_mask):
        return '-i %s -j MARK --set-xmark %s' % (device_name, mark_mask)

    def address_scope_filter_rule(self, device_name, mark_mask):
        return '-o %s -m mark ! --mark %s -j DROP' % (
            device_name, mark_mask)

    def ipv6_fip_admin_filter_rule(self, device_name, floating_ip):
        traffic_to_floating_ip = (
            '-i %s -d %s/128 -j DROP' %
            (device_name, floating_ip))
        traffic_from_floating_ip = (
            '-o %s -s %s/128 -j DROP' %
            (device_name, floating_ip))
        return [traffic_to_floating_ip, traffic_from_floating_ip]

    def port_num_ipv4_rule(self, device_name, ip_addr, port_num):
        denied_port_tcp = ('-d %s/32 -i %s -p tcp -m tcp --dport %s -j DROP'
                        % (ip_addr, device_name, port_num))
        denied_port_udp = ('-d %s/32 -i %s -p udp -m udp --dport %s -j DROP'
                        % (ip_addr, device_name, port_num))
        return [denied_port_tcp, denied_port_udp]

    def port_num_ipv6_rule(self, device_name, fixed_ip, port_num):
        denied_tcp_port = ('-d %s/128 -i %s -p tcp -m tcp --dport %s -j DROP'
                        % (fixed_ip, device_name, port_num))
        denied_udp_port = ('-d %s/128 -i %s -p udp -m udp --dport %s -j DROP'
                        % (fixed_ip, device_name, port_num))
        return [denied_tcp_port, denied_udp_port]

    def _process_internal_ports(self):
        existing_port_ids = set(p['id'] for p in self.internal_ports)

        internal_ports = self.router.get(lib_constants.INTERFACE_KEY, [])
        current_port_ids = set(p['id'] for p in internal_ports
                               if p['admin_state_up'])

        new_port_ids = current_port_ids - existing_port_ids
        new_ports = [p for p in internal_ports if p['id'] in new_port_ids]
        old_ports = [p for p in self.internal_ports
                     if p['id'] not in current_port_ids]
        updated_ports = self._get_updated_ports(self.internal_ports,
                                                internal_ports)

        enable_ra = False
        for p in old_ports:
            self.internal_network_removed(p)
            LOG.debug("removing port %s from internal_ports cache", p)
            self.internal_ports.remove(p)
            enable_ra = enable_ra or self._port_has_ipv6_subnet(p)
            for subnet in p['subnets']:
                if ipv6_utils.is_ipv6_pd_enabled(subnet):
                    self.backend.pd.disable_subnet(
                        self.router_id, subnet['id'])
                    del self.pd_subnets[subnet['id']]

        for p in new_ports:
            self.internal_network_added(p)
            LOG.debug("appending port %s to internal_ports cache", p)
            self._update_internal_ports_cache(p)
            enable_ra = enable_ra or self._port_has_ipv6_subnet(p)
            for subnet in p.get('subnets', []):
                if ipv6_utils.is_ipv6_pd_enabled(subnet):
                    interface_name = self.get_internal_device_name(p['id'])
                    self.backend.pd.enable_subnet(
                        self.router_id, subnet['id'], subnet['cidr'],
                        interface_name, p['mac_address'])
                    if (subnet['cidr'] !=
                            lib_constants.PROVISIONAL_IPV6_PD_PREFIX):
                        self.pd_subnets[subnet['id']] = subnet['cidr']

        updated_cidrs = []
        for p in updated_ports:
            self._update_internal_ports_cache(p)
            interface_name = self.get_internal_device_name(p['id'])
            ip_cidrs = common_utils.fixed_ip_cidrs(p['fixed_ips'])
            LOG.debug("updating internal network for port %s", p)
            updated_cidrs += ip_cidrs
            self.internal_network_updated(
                interface_name, ip_cidrs, p['mtu'])
            enable_ra = enable_ra or self._port_has_ipv6_subnet(p)

        # Check if there is any pd prefix update
        for p in internal_ports:
            if p['id'] in (set(current_port_ids) & set(existing_port_ids)):
                for subnet in p.get('subnets', []):
                    if ipv6_utils.is_ipv6_pd_enabled(subnet):
                        old_prefix = self.backend.pd.update_subnet(
                            self.router_id,
                            subnet['id'],
                            subnet['cidr'])
                        if old_prefix:
                            self._internal_network_updated(p, subnet['id'],
                                                           subnet['cidr'],
                                                           old_prefix,
                                                           updated_cidrs)
                            self.pd_subnets[subnet['id']] = subnet['cidr']
                            enable_ra = True

        # Enable RA
        if enable_ra:
            self.enable_radvd(internal_ports)

        existing_devices = self._get_existing_devices()
        current_internal_devs = set(n for n in existing_devices
                                    if n.startswith(INTERNAL_DEV_PREFIX))
        current_port_devs = set(self.get_internal_device_name(port_id)
                                for port_id in current_port_ids)
        stale_devs = current_internal_devs - current_port_devs
        for stale_dev in stale_devs:
            LOG.debug('Deleting stale internal router device: %s',
                      stale_dev)
            self.backend.pd.remove_stale_ri_ifname(
                self.router_id, stale_dev)
            self.driver.unplug(
                stale_dev, namespace=self.ns_name, prefix=INTERNAL_DEV_PREFIX)

    def _list_floating_ip_cidrs(self):
        # Compute a list of addresses this router is supposed to have.
        # This avoids unnecessarily removing those addresses and
        # causing a momentarily network outage.
        floating_ips = self.get_floating_ips()
        return [common_utils.ip_to_cidr(ip['floating_ip_address'])
                for ip in floating_ips]

    def _plug_external_gateway(self, ex_gw_port, interface_name, ns_name,
                               link_up=True):
        self.driver.plug(ex_gw_port['network_id'],
                         ex_gw_port['id'],
                         interface_name,
                         ex_gw_port['mac_address'],
                         namespace=ns_name,
                         prefix=EXTERNAL_DEV_PREFIX,
                         mtu=ex_gw_port.get('mtu'),
                         link_up=link_up)

    def _get_external_gw_ips(self, ex_gw_port):
        gateway_ips = []
        if 'subnets' in ex_gw_port:
            gateway_ips = [subnet['gateway_ip']
                           for subnet in ex_gw_port['subnets']
                           if subnet['gateway_ip']]
        if self.use_ipv6 and not self.is_v6_gateway_set(gateway_ips):
            # No IPv6 gateway is available, but IPv6 is enabled.
            if self.agent_conf.ipv6_gateway:
                # ipv6_gateway configured, use address for default route.
                gateway_ips.append(self.agent_conf.ipv6_gateway)
        return gateway_ips

    def _add_route_to_gw(self, ex_gw_port, device_name,
                         namespace, preserve_ips):
        # Note: ipv6_gateway is an ipv6 LLA
        # and so doesn't need a special route
        for subnet in ex_gw_port.get('subnets', []):
            is_gateway_not_in_subnet = (subnet['gateway_ip'] and
                                        not ipam_utils.check_subnet_ip(
                                                subnet['cidr'],
                                                subnet['gateway_ip']))
            if is_gateway_not_in_subnet:
                preserve_ips.append(subnet['gateway_ip'])
                device = ip_lib.IPDevice(device_name, namespace=namespace)
                device.route.add_route(subnet['gateway_ip'], scope='link')

    def _configure_ipv6_params_on_gw(self, ex_gw_port, ns_name, interface_name,
                                     enabled):
        if not self.use_ipv6:
            return

        disable_ra = not enabled
        if enabled:
            gateway_ips = self._get_external_gw_ips(ex_gw_port)
            if not self.is_v6_gateway_set(gateway_ips):
                # There is no IPv6 gw_ip, use RouterAdvt for default route.
                self.driver.configure_ipv6_ra(
                    ns_name, interface_name, n_const.ACCEPT_RA_WITH_FORWARDING)
            else:
                # Otherwise, disable it
                disable_ra = True
        if disable_ra:
            self.driver.configure_ipv6_ra(
                ns_name, interface_name, n_const.ACCEPT_RA_DISABLED)
        self.driver.configure_ipv6_forwarding(
            ns_name, interface_name, enabled)
        # This will make sure the 'all' setting is the same as the interface,
        # which is needed for forwarding to work.  Don't disable once it's
        # been enabled so as to not send spurious MLDv2 packets out.
        if enabled:
            self.driver.configure_ipv6_forwarding(
                ns_name, 'all', enabled)

    def _external_gateway_added(self, ex_gw_port, interface_name,
                                ns_name, preserve_ips):
        LOG.debug("External gateway added: port(%s), interface(%s), ns(%s)",
                  ex_gw_port, interface_name, ns_name)
        self._plug_external_gateway(ex_gw_port, interface_name, ns_name)
        self._external_gateway_settings(ex_gw_port, interface_name,
                                        ns_name, preserve_ips)

    def _external_gateway_settings(self, ex_gw_port, interface_name,
                                   ns_name, preserve_ips):
        # Build up the interface and gateway IP addresses that
        # will be added to the interface.
        ip_cidrs = common_utils.fixed_ip_cidrs(ex_gw_port['fixed_ips'])

        gateway_ips = self._get_external_gw_ips(ex_gw_port)

        self._add_route_to_gw(ex_gw_port, device_name=interface_name,
                              namespace=ns_name, preserve_ips=preserve_ips)
        self.driver.init_router_port(
            interface_name,
            ip_cidrs,
            namespace=ns_name,
            extra_subnets=ex_gw_port.get('extra_subnets', []),
            preserve_ips=preserve_ips,
            clean_connections=True)

        device = ip_lib.IPDevice(interface_name, namespace=ns_name)
        current_gateways = set()
        for ip_version in (lib_constants.IP_VERSION_4,
                           lib_constants.IP_VERSION_6):
            gateway = device.route.get_gateway(ip_version=ip_version)
            if gateway and gateway.get('gateway'):
                current_gateways.add(gateway.get('gateway'))
        for ip in current_gateways - set(gateway_ips):
            device.route.delete_gateway(ip)
        for ip in gateway_ips:
            device.route.add_gateway(ip)

        self._configure_ipv6_params_on_gw(ex_gw_port, ns_name, interface_name,
                                          True)

        for fixed_ip in ex_gw_port['fixed_ips']:
            ip_lib.send_ip_addr_adv_notif(ns_name,
                                          interface_name,
                                          fixed_ip['ip_address'])

    def is_v6_gateway_set(self, gateway_ips):
        """Check to see if list of gateway_ips has an IPv6 gateway.
        """
        # Note - don't require a try-except here as all
        # gateway_ips elements are valid addresses, if they exist.
        return any(netaddr.IPAddress(gw_ip).version == 6
                   for gw_ip in gateway_ips)

    def get_router_preserve_ips(self):
        preserve_ips = self._list_floating_ip_cidrs() + list(
            self.centralized_port_forwarding_fip_set) + list(
            self.centralize_floatingip_esnat_ip_set)
        preserve_ips.extend(self.backend.pd.get_preserve_ips(self.router_id))
        return preserve_ips

    def external_gateway_added(self, ex_gw_port, interface_name):
        preserve_ips = self.get_router_preserve_ips()
        self._external_gateway_added(
            ex_gw_port, interface_name, self.ns_name, preserve_ips)

    def external_gateway_updated(self, ex_gw_port, interface_name):
        preserve_ips = self.get_router_preserve_ips()
        self._external_gateway_added(
            ex_gw_port, interface_name, self.ns_name, preserve_ips)

    def external_gateway_removed(self, ex_gw_port, interface_name):
        LOG.debug("External gateway removed: port(%s), interface(%s)",
                  ex_gw_port, interface_name)
        device = ip_lib.IPDevice(interface_name, namespace=self.ns_name)
        for ip_addr in ex_gw_port['fixed_ips']:
            prefixlen = ip_addr.get('prefixlen')
            self.remove_external_gateway_ip(device,
                                            common_utils.ip_to_cidr(
                                                ip_addr['ip_address'],
                                                prefixlen))
        self.driver.unplug(interface_name,
                           namespace=self.ns_name,
                           prefix=EXTERNAL_DEV_PREFIX)

    @staticmethod
    def _gateway_ports_equal(port1, port2):
        return port1 == port2

    def _delete_stale_external_devices(self, interface_name):
        existing_devices = self._get_existing_devices()
        stale_devs = [dev for dev in existing_devices
                      if dev.startswith(EXTERNAL_DEV_PREFIX) and
                      dev != interface_name]
        for stale_dev in stale_devs:
            LOG.debug('Deleting stale external router device: %s', stale_dev)
            self.backend.pd.remove_gw_interface(self.router['id'])
            self.driver.unplug(stale_dev,
                               namespace=self.ns_name,
                               prefix=EXTERNAL_DEV_PREFIX)

    def _process_external_gateway(self, ex_gw_port):
        # TODO(Carl) Refactor to clarify roles of ex_gw_port vs self.ex_gw_port
        ex_gw_port_id = (ex_gw_port and ex_gw_port['id'] or
                         self.ex_gw_port and self.ex_gw_port['id'])

        interface_name = None
        if ex_gw_port_id:
            interface_name = self.get_external_device_name(ex_gw_port_id)
        if ex_gw_port:
            if not self.ex_gw_port:
                self.external_gateway_added(ex_gw_port, interface_name)
                self.backend.pd.add_gw_interface(self.router['id'],
                                                 interface_name)
            elif not self._gateway_ports_equal(ex_gw_port, self.ex_gw_port):
                self.external_gateway_updated(ex_gw_port, interface_name)
        elif not ex_gw_port and self.ex_gw_port:
            self.external_gateway_removed(self.ex_gw_port, interface_name)
            self.backend.pd.remove_gw_interface(self.router['id'])
        elif not ex_gw_port and not self.ex_gw_port:
            for p in self.internal_ports:
                interface_name = self.get_internal_device_name(p['id'])
                self.gateway_redirect_cleanup(interface_name)

        self._delete_stale_external_devices(interface_name)

        # Process SNAT rules for external gateway
        gw_port = self._router.get('gw_port')
        self._handle_router_snat_rules(gw_port, interface_name)

    def _prevent_snat_for_internal_traffic_rule(self, interface_name):
        return (
            'POSTROUTING', '! -i %(interface_name)s '
                           '! -o %(interface_name)s -m conntrack ! '
                           '--ctstate DNAT -j ACCEPT' %
                           {'interface_name': interface_name})

    def external_gateway_nat_fip_rules(self, ex_gw_ip, interface_name):
        dont_snat_traffic_to_internal_ports_if_not_to_floating_ip = (
            self._prevent_snat_for_internal_traffic_rule(interface_name))
        # Makes replies come back through the router to reverse DNAT
        ext_in_mark = self.agent_conf.external_ingress_mark
        to_source = ('-m mark ! --mark %s/%s '
                     '-m conntrack --ctstate DNAT '
                     '-j SNAT --to-source %s'
                     % (ext_in_mark, n_const.ROUTER_MARK_MASK, ex_gw_ip))
        if self.iptables_manager.random_fully:
            to_source += ' --random-fully'
        snat_internal_traffic_to_floating_ip = ('snat', to_source)
        return [dont_snat_traffic_to_internal_ports_if_not_to_floating_ip,
                snat_internal_traffic_to_floating_ip]

    def external_gateway_nat_snat_rules(self, ex_gw_ip, interface_name):
        to_source = '-o %s -j SNAT --to-source %s' % (interface_name, ex_gw_ip)
        if self.iptables_manager.random_fully:
            to_source += ' --random-fully'
        return [('snat', to_source)]

    def external_gateway_mangle_rules(self, interface_name):
        mark = self.agent_conf.external_ingress_mark
        mark_packets_entering_external_gateway_port = (
            'mark', '-i %s -j MARK --set-xmark %s/%s' %
                    (interface_name, mark, n_const.ROUTER_MARK_MASK))
        return [mark_packets_entering_external_gateway_port]

    def _empty_snat_chains(self, iptables_manager):
        iptables_manager.ipv4['nat'].empty_chain('POSTROUTING')
        iptables_manager.ipv4['nat'].empty_chain('snat')
        iptables_manager.ipv4['mangle'].empty_chain('mark')
        iptables_manager.ipv4['mangle'].empty_chain('POSTROUTING')
        if self.agent_conf.enable_nat6to6:
            iptables_manager.ipv6['nat'].empty_chain('POSTROUTING')
            iptables_manager.ipv6['nat'].empty_chain('snat')
            iptables_manager.ipv6['mangle'].empty_chain('mark')
            iptables_manager.ipv6['mangle'].empty_chain('POSTROUTING')

    def _add_snat_rules(self, ex_gw_port, iptables_manager,
                        interface_name):
        self.process_external_port_address_scope_routing(iptables_manager)

        if ex_gw_port:
            if self.agent.enable_elastic_snat:
                iptables_manager.ipv4['nat'].add_chain('esnat')
                iptables_manager.ipv4['nat'].add_rule('snat', '-j $esnat')

            # ex_gw_port should not be None in this case
            # NAT rules are added only if ex_gw_port has an IPv4 address
            for ip_addr in ex_gw_port['fixed_ips']:
                ex_gw_ip = ip_addr['ip_address']
                if netaddr.IPAddress(ex_gw_ip).version == 4:
                    if self._snat_enabled:
                        rules = self.external_gateway_nat_snat_rules(
                            ex_gw_ip, interface_name)
                        for rule in rules:
                            iptables_manager.ipv4['nat'].add_rule(*rule)

                    rules = self.external_gateway_nat_fip_rules(
                        ex_gw_ip, interface_name)
                    for rule in rules:
                        iptables_manager.ipv4['nat'].add_rule(*rule)
                    rules = self.external_gateway_mangle_rules(interface_name)
                    for rule in rules:
                        iptables_manager.ipv4['mangle'].add_rule(*rule)
                    iptables_manager.ipv4['nat'].add_rule(
                        'neutron-postrouting-bottom', '-j $POSTROUTING',
                        wrap=False, top=True, tag='jump_to_$POSTROUTING')
                    break

            if not self.agent_conf.enable_nat6to6:
                return
            for ip_addr in ex_gw_port['fixed_ips']:
                ex_gw_ip = ip_addr['ip_address']
                if netaddr.IPAddress(ex_gw_ip).version == 6:
                    if self._snat66_enabled:
                        rules = self.external_gateway_nat_snat_rules(
                            ex_gw_ip, interface_name)
                        for rule in rules:
                            iptables_manager.ipv6['nat'].add_rule(*rule)

                    rules = self.external_gateway_nat_fip_rules(
                        ex_gw_ip, interface_name)
                    for rule in rules:
                        iptables_manager.ipv6['nat'].add_rule(*rule)
                    rules = self.external_gateway_mangle_rules(interface_name)
                    for rule in rules:
                        iptables_manager.ipv6['mangle'].add_rule(*rule)
                    iptables_manager.ipv6['nat'].add_rule(
                        'neutron-postrouting-bottom', '-j $POSTROUTING',
                        wrap=False, top=True, tag='jump_to_$POSTROUTING')
                    break

    def _handle_router_snat_rules(self, ex_gw_port, interface_name):
        self._empty_snat_chains(self.iptables_manager)

        self.iptables_manager.ipv4['nat'].clear_rules_by_tag(
            'jump_to_$POSTROUTING')
        if self.agent_conf.enable_nat6to6:
            self.iptables_manager.ipv6['nat'].clear_rules_by_tag(
                'jump_to_$POSTROUTING')

        self.iptables_manager.ipv4['nat'].add_rule('snat', '-j $float-snat')
        if self.agent_conf.enable_nat6to6:
            self.iptables_manager.ipv6['nat'].add_rule(
                'snat', '-j $float-snat')

        self._add_snat_rules(ex_gw_port,
                             self.iptables_manager,
                             interface_name)

    def _process_external_on_delete(self):
        fip_statuses = {}
        try:
            ex_gw_port = self.get_ex_gw_port()
            self._process_external_gateway(ex_gw_port)
            if not ex_gw_port:
                return

            interface_name = self.get_external_device_interface_name(
                ex_gw_port)
            fip_statuses = self.configure_fip_addresses(interface_name)

        except n_exc.FloatingIpSetupException:
            # All floating IPs must be put in error state
            LOG.exception("Failed to process floating IPs.")
            fip_statuses = self.put_fips_in_error_state()
        finally:
            self.update_fip_statuses(fip_statuses)

    def process_external(self):
        fip_statuses = {}
        try:
            with self.iptables_manager.defer_apply():
                ex_gw_port = self.get_ex_gw_port()
                self._process_external_gateway(ex_gw_port)
                if not ex_gw_port:
                    return

                # Process SNAT/DNAT rules and addresses for floating IPs
                self.process_snat_dnat_for_fip()

            # Once NAT rules for floating IPs are safely in place
            # configure their addresses on the external gateway port
            interface_name = self.get_external_device_interface_name(
                ex_gw_port)
            fip_statuses = self.configure_fip_addresses(interface_name)

        except n_exc.FloatingIpSetupException:
            # All floating IPs must be put in error state
            LOG.exception("Failed to process floating IPs.")
            fip_statuses = self.put_fips_in_error_state()
        except n_exc.IpTablesApplyException:
            with excutils.save_and_reraise_exception():
                # All floating IPs must be put in error state
                LOG.error("Failed to process floating IPs")
                fip_statuses = self.put_fips_in_error_state()
        finally:
            self.update_fip_statuses(fip_statuses)

    def update_fip_statuses(self, fip_statuses):
        # Identify floating IPs which were disabled
        existing_floating_ips = self.floating_ips
        self.floating_ips = set(fip_statuses.keys())
        for fip_id in existing_floating_ips - self.floating_ips:
            fip_statuses[fip_id] = lib_constants.FLOATINGIP_STATUS_DOWN
        # filter out statuses that didn't change
        fip_statuses = {f: stat for f, stat in fip_statuses.items()
                        if stat != FLOATINGIP_STATUS_NOCHANGE}
        if not fip_statuses:
            return
        LOG.debug('Sending floating ip statuses: %s', fip_statuses)
        # Update floating IP status on the neutron server
        self.agent.plugin_rpc.update_floatingip_statuses(
            self.agent.context, self.router_id, fip_statuses)

    def initialize_address_scope_iptables(self):
        self._initialize_address_scope_iptables(self.iptables_manager)

    def _initialize_address_scope_iptables(self, iptables_manager):
        # Add address scope related chains
        iptables_manager.ipv4['mangle'].add_chain('scope')
        iptables_manager.ipv6['mangle'].add_chain('scope')

        iptables_manager.ipv4['mangle'].add_chain('floatingip')
        iptables_manager.ipv4['mangle'].add_chain('float-snat')

        iptables_manager.ipv4['filter'].add_chain('scope')
        iptables_manager.ipv6['filter'].add_chain('scope')
        iptables_manager.ipv4['filter'].add_rule('FORWARD', '-j $scope')
        iptables_manager.ipv6['filter'].add_rule('FORWARD', '-j $scope')

        # Add rules for marking traffic for address scopes
        mark_new_ingress_address_scope_by_interface = (
            '-j $scope')
        copy_address_scope_for_existing = (
            '-m connmark ! --mark 0x0/0xffff0000 '
            '-j CONNMARK --restore-mark '
            '--nfmask 0xffff0000 --ctmask 0xffff0000')
        mark_new_ingress_address_scope_by_floatingip = (
            '-j $floatingip')
        save_mark_to_connmark = (
            '-m connmark --mark 0x0/0xffff0000 '
            '-j CONNMARK --save-mark '
            '--nfmask 0xffff0000 --ctmask 0xffff0000')

        iptables_manager.ipv4['mangle'].add_rule(
            'PREROUTING', mark_new_ingress_address_scope_by_interface)
        iptables_manager.ipv4['mangle'].add_rule(
            'PREROUTING', copy_address_scope_for_existing)
        # The floating ip scope rules must come after the CONNTRACK rules
        # because the (CONN)MARK targets are non-terminating (this is true
        # despite them not being documented as such) and the floating ip
        # rules need to override the mark from CONNMARK to cross scopes.
        iptables_manager.ipv4['mangle'].add_rule(
            'PREROUTING', mark_new_ingress_address_scope_by_floatingip)
        iptables_manager.ipv4['mangle'].add_rule(
            'float-snat', save_mark_to_connmark)
        iptables_manager.ipv6['mangle'].add_rule(
            'PREROUTING', mark_new_ingress_address_scope_by_interface)
        iptables_manager.ipv6['mangle'].add_rule(
            'PREROUTING', copy_address_scope_for_existing)

    def initialize_address_fip_admin_iptables(self):
        self._initialize_address_fip_admin_iptables(self.iptables_manager)

    def _initialize_address_fip_admin_iptables(self, iptables_manager):
        # Add address fip admin related chains
        iptables_manager.ipv4['raw'].add_chain('fip-admin')
        iptables_manager.ipv4['raw'].add_rule('PREROUTING', '-j $fip-admin')
        if self.agent_conf.enable_nat6to6:
            iptables_manager.ipv6['raw'].add_chain('fip-admin')
            iptables_manager.ipv6['raw'].add_rule(
                'PREROUTING', '-j $fip-admin')
        iptables_manager.ipv4['filter'].add_chain('fip-admin')
        iptables_manager.ipv4['filter'].add_rule('FORWARD', '-j $fip-admin')
        iptables_manager.ipv6['filter'].add_chain('fip-admin')
        iptables_manager.ipv6['filter'].add_rule('FORWARD', '-j $fip-admin')

    def initialize_fip_denied_ports_iptables(self):
        self._initialize_fip_denied_ports_iptables(self.iptables_manager)

    def _initialize_fip_denied_ports_iptables(self, iptables_manager):
        iptables_manager.ipv4['raw'].add_chain('denied-port')
        iptables_manager.ipv4['raw'].add_rule('PREROUTING', '-j $denied-port')
        if self.agent_conf.enable_nat6to6:
            iptables_manager.ipv6['raw'].add_chain('denied-port')
            iptables_manager.ipv6['raw'].add_rule(
                'PREROUTING', '-j $denied-port')
        else:
            iptables_manager.ipv6['filter'].add_chain('denied-port')
            iptables_manager.ipv6['filter'].add_rule(
                'FORWARD', '-j $denied-port')

    def initialize_route_table_raw_iptables(self):
        self._initialize_route_table_raw_iptables(self.iptables_manager)

    def _initialize_route_table_raw_iptables(self, iptables_manager):
        iptables_manager.ipv4['raw'].add_chain('pre-accept')
        iptables_manager.ipv4['raw'].add_rule('PREROUTING', '-j $pre-accept')
        iptables_manager.ipv4['raw'].add_chain('pre-notrack')
        iptables_manager.ipv4['raw'].add_rule('PREROUTING', '-j $pre-notrack')

    def initialize_metadata_iptables(self):
        # Always mark incoming metadata requests, that way any stray
        # requests that arrive before the filter metadata redirect
        # rule is installed will be dropped.
        mark_metadata_for_internal_interfaces = (
            '-d ***************/32 '
            '-i %(interface_name)s '
            '-p tcp -m tcp --dport 80 '
            '-j MARK --set-xmark %(value)s/%(mask)s' %
            {'interface_name': INTERNAL_DEV_PREFIX + '+',
             'value': self.agent_conf.metadata_access_mark,
             'mask': n_const.ROUTER_MARK_MASK})
        self.iptables_manager.ipv4['mangle'].add_rule(
            'PREROUTING', mark_metadata_for_internal_interfaces)
        if self.agent_conf.enable_metrics_proxy and \
                self.agent_conf.metrics_proxy_socket:
            mark_metrics_proxy_for_internal_interfaces = (
                '-d ***************/32 '
                '-i %(interface_name)s '
                '-p tcp -m tcp --dport 81 '
                '-j MARK --set-xmark %(value)s/%(mask)s' %
                {'interface_name': INTERNAL_DEV_PREFIX + '+',
                 'value': self.agent_conf.metadata_access_mark,
                 'mask': n_const.ROUTER_MARK_MASK})
            self.iptables_manager.ipv4['mangle'].add_rule(
                'PREROUTING',
                mark_metrics_proxy_for_internal_interfaces)

    def _get_port_devicename_scopemark(self, ports, name_generator):
        devicename_scopemark = {lib_constants.IP_VERSION_4: dict(),
                                lib_constants.IP_VERSION_6: dict()}
        for p in ports:
            device_name = name_generator(p['id'])
            ip_cidrs = common_utils.fixed_ip_cidrs(p['fixed_ips'])
            port_as_marks = self.get_port_address_scope_mark(p)
            for ip_version in {common_utils.get_ip_version(cidr)
                               for cidr in ip_cidrs}:
                devicename_scopemark[ip_version][device_name] = (
                    port_as_marks[ip_version])

        return devicename_scopemark

    def _get_address_scope_mark(self):
        # Prepare address scope iptables rule for internal ports
        internal_ports = self.router.get(lib_constants.INTERFACE_KEY, [])
        ports_scopemark = self._get_port_devicename_scopemark(
            internal_ports, self.get_internal_device_name)

        # Prepare address scope iptables rule for external port
        external_port = self.get_ex_gw_port()
        if external_port:
            external_port_scopemark = self._get_port_devicename_scopemark(
                [external_port], self.get_external_device_name)
            for ip_version in (lib_constants.IP_VERSION_4,
                               lib_constants.IP_VERSION_6):
                ports_scopemark[ip_version].update(
                    external_port_scopemark[ip_version])
        return ports_scopemark

    def _add_address_scope_mark(self, iptables_manager, ports_scopemark):
        external_device_name = None
        external_port = self.get_ex_gw_port()
        if external_port:
            external_device_name = self.get_external_device_name(
                external_port['id'])

        # Process address scope iptables rules
        for ip_version in (lib_constants.IP_VERSION_4,
                           lib_constants.IP_VERSION_6):
            scopemarks = ports_scopemark[ip_version]
            iptables = iptables_manager.get_tables(ip_version)
            iptables['mangle'].empty_chain('scope')
            iptables['filter'].empty_chain('scope')
            dont_block_external = (ip_version == lib_constants.IP_VERSION_4 and
                                   self._snat_enabled and external_port)
            for device_name, mark in scopemarks.items():
                # Add address scope iptables rule
                iptables['mangle'].add_rule(
                    'scope',
                    self.address_scope_mangle_rule(device_name, mark))
                if dont_block_external and device_name == external_device_name:
                    continue
                iptables['filter'].add_rule(
                    'scope',
                    self.address_scope_filter_rule(device_name, mark))
        for subnet_id, prefix in self.pd_subnets.items():
            if prefix != lib_constants.PROVISIONAL_IPV6_PD_PREFIX:
                self._process_pd_iptables_rules(prefix, subnet_id)

    def process_ports_address_scope_iptables(self):
        ports_scopemark = self._get_address_scope_mark()
        self._add_address_scope_mark(self.iptables_manager, ports_scopemark)

    def _get_external_address_scope(self):
        external_port = self.get_ex_gw_port()
        if not external_port:
            return

        scopes = external_port.get('address_scopes', {})
        return scopes.get(str(lib_constants.IP_VERSION_4))

    def process_external_port_address_scope_routing(self, iptables_manager):
        if not self._snat_enabled:
            return

        external_port = self.get_ex_gw_port()
        if not external_port:
            return

        external_devicename = self.get_external_device_name(
            external_port['id'])

        # Saves the originating address scope by saving the packet MARK to
        # the CONNMARK for new connections so that returning traffic can be
        # match to it.
        rule = ('-o %s -m connmark --mark 0x0/0xffff0000 '
                '-j CONNMARK --save-mark '
                '--nfmask 0xffff0000 --ctmask 0xffff0000' %
                external_devicename)

        iptables_manager.ipv4['mangle'].add_rule('POSTROUTING', rule)

        address_scope = self._get_external_address_scope()
        if not address_scope:
            return

        # Prevents snat within the same address scope
        rule = '-o %s -m connmark --mark %s -j ACCEPT' % (
            external_devicename,
            self.get_address_scope_mark_mask(address_scope))
        iptables_manager.ipv4['nat'].add_rule('snat', rule)

    def process_address_scope(self):
        with self.iptables_manager.defer_apply():
            self.process_ports_address_scope_iptables()
            self.process_floating_ip_address_scope_rules()

    def process_address_fip_admin(self):
        with self.iptables_manager.defer_apply():
            self.process_floating_ip_address_fip_admin_rules()

    def process_fip_denied_ports(self):
        with self.iptables_manager.defer_apply():
            self.process_fip_denied_ports_rules()

    @common_utils.exception_logger()
    def process_delete(self):
        """Process the delete of this router

        This method is the point where the agent requests that this router
        be deleted. This is a separate code path from process in that it
        avoids any changes to the qrouter namespace that will be removed
        at the end of the operation.
        """
        LOG.debug("Process delete, router %s", self.router['id'])
        if self.router_namespace.exists():
            self._process_internal_ports()
            self.backend.sync_router_pd(self.router['id'])
            self._process_external_on_delete()
        else:
            LOG.warning("Can't gracefully delete the router %s: "
                        "no router namespace found", self.router['id'])

    @common_utils.exception_logger()
    def process(self):
        LOG.debug("Process updates, router %s", self.router['id'])
        self.centralized_port_forwarding_fip_set = set(self.router.get(
            'port_forwardings_fip_set', set()))
        self.centralize_floatingip_esnat_ip_set = set(self.router.get(
            'elastic_snat_fip_set', set()))
        self._process_internal_ports()
        self.backend.sync_router_pd(self.router['id'])
        self.process_external()
        self.process_address_scope()
        if self.agent_conf.wo_enable_fip_admin_state_up:
            self.process_address_fip_admin()
        if self.agent_conf.enable_wo_fip_ad_ports:
            self.process_fip_denied_ports()
        # Process static routes for router
        self.routes_updated(self.routes, self.router['routes'])
        self.routes = self.router['routes']

        # Update ex_gw_port on the router info cache
        self.ex_gw_port = self.get_ex_gw_port()
        self.fip_map = dict([(fip['floating_ip_address'],
                              fip['fixed_ip_address'])
                             for fip in self.get_floating_ips()])
        self.fip_managed_by_port_forwardings = self.router.get(
            'fip_managed_by_port_forwardings')
        self.fip_managed_by_elastic_snats = self.router.get(
            'fip_managed_by_elastic_snats')

    def check_router_namespace(self):
        result = {"status": True,
                  "namespace_error": ""}
        if self.router_namespace.exists() is False:
            result["status"] = False
            result["namespace_error"] = ("Router namespace %s does not exist."
                                         % self.get_gw_ns_name())
        return result

    def check_router_worker_process(self):
        result = {"status": True,
                  "router_worker_process_error": []}
        if self.router_namespace.exists() is False:
            result['status'] = False
            return result
        process_monitor = (self.backend.process_monitor
                           ._monitored_processes)
        router_id = self.router_id
        radvd = 'radvd'
        keepalived = 'keepalived'
        ha_proxy = 'metadata-proxy'
        ip_monitor = 'ip_monitor'
        worker_process = {}
        for service, process_manager in process_monitor.items():
            if service.uuid == router_id:
                worker_process[service.service] = process_manager

        # Check radvd process
        radvd_running = False
        for port in self.internal_ports:
            if radvd_running:
                break
            for subnet in port['subnets']:
                if netaddr.IPNetwork(subnet['cidr']).version == 6:
                    radvd_running = True
                    break
        if radvd_running:
            self._check_process_active(worker_process.get(radvd),
                                      "radvd",
                                       result)

        ha = self.router.get('ha', False)
        if ha:
            # Check keepalived and keepalived-state-change process
            self._check_process_active(worker_process.get(keepalived),
                                      "keepalived",
                                       result)
            self._check_process_active(worker_process.get(ip_monitor),
                                      "keepalived-state-change",
                                       result)

            # Check haproxy process
            if self.agent_conf.enable_metadata_proxy:
                self._check_process_active(worker_process.get(ha_proxy),
                                          "haproxy",
                                           result)

        return result

    def _check_process_active(self, process, process_name, result):
        if (process is None or not process.active):
            result["status"] = False
            result["router_worker_process_error"].append(
                "%s process is not running." % process_name)

    def check_router_iptables(self):
        result = {"status": True,
                  "iptables_error": []}
        if self.router_namespace.exists() is False:
            result['status'] = False
            return result
        ns_tables = self.get_ns_iptables_rule()
        ns_tables = self._normalize_ns_tables(ns_tables)
        if self.ex_gw_port:
            interface_name = self.get_external_device_name(
                self.ex_gw_port['id'])

            # Check external gateway iptables rules(IPV4)
            self.check_external_gateway_iptables_ipv4_rules(
                interface_name, result, ns_tables)

            # Check external gateway iptables rules(IPV6)
            self.check_external_gateway_iptables_ipv6_rules(
                interface_name, result, ns_tables)

            # Check floating ip nat rules
            self.check_floating_ip_iptables(
                result, ns_tables)

            # Check elastic snat nat rules
            elastic_snat_rules = self.router.get(
                '_elastic_snat_rules', [])
            self.check_esnat_rules(interface_name,
                                  elastic_snat_rules,
                                  result, ns_tables)
            # Check port forwarding nat rules
            self.check_port_forwarding_rules(result, ns_tables)

        # Check address scope iptables rules
        self.check_address_scope_iptables(
            result, ns_tables)
        if self.agent_conf.wo_enable_fip_admin_state_up:
            self.check_floating_ip_address_fip_admin_rules(
                result, ns_tables)
        if self.agent_conf.enable_wo_fip_ad_ports:
            self.check_fip_denied_ports_rules(
                result, ns_tables)

        return result

    def check_external_gateway_iptables_ipv4_rules(
            self, interface_name, result, ns_tables):
        # Check external gateway iptables rules(IPV4)
        for ip_addr in self.ex_gw_port['fixed_ips']:
            ex_gw_ip = ip_addr['ip_address']
            if netaddr.IPAddress(ex_gw_ip).version == 4:
                # Check SNAT iptables rules,
                # if the router's enable-snat attribute is True.
                if self._snat_enabled:
                    rules = self.external_gateway_nat_snat_rules(
                        ex_gw_ip, interface_name)
                    self._check_iptables_rules_exist(
                        'nat', rules, result, ns_tables)

                # Check FIP iptables rules. Ensuring that only
                # traffic destined to a floating IP is SNATed,
                # while other traffic is left as is, is very
                # important for the functionality of floating IPs.
                rules = self.external_gateway_nat_fip_rules(
                    ex_gw_ip, interface_name)
                self._check_iptables_rules_exist(
                    'nat', rules, result, ns_tables)
                rules = self.external_gateway_mangle_rules(
                    interface_name)
                self._check_iptables_rules_exist(
                    'mangle', rules, result, ns_tables)
                break

    def check_external_gateway_iptables_ipv6_rules(
            self, interface_name, result, ns_tables):
        # Check external gateway iptables rules(IPV6)
        if self.agent_conf.enable_nat6to6:
            for ip_addr in self.ex_gw_port['fixed_ips']:
                ex_gw_ip = ip_addr['ip_address']
                if netaddr.IPAddress(ex_gw_ip).version == 6:
                    if self._snat66_enabled:
                        rules = (
                            self.external_gateway_nat_snat_rules(
                                ex_gw_ip, interface_name))
                        self._check_iptables_rules_exist(
                            'nat', rules, result,
                            ns_tables, 'ipv6')

                    rules = self.external_gateway_nat_fip_rules(
                        ex_gw_ip, interface_name)
                    self._check_iptables_rules_exist(
                        'nat', rules, result, ns_tables, 'ipv6')
                    rules = (
                        self.external_gateway_mangle_rules(
                            interface_name))
                    self._check_iptables_rules_exist(
                        'mangle', rules, result, ns_tables, 'ipv6')
                    break

    def check_fip_denied_ports_rules(self, result, ns_tables):
        ex_gw_port = self.get_ex_gw_port()
        if not ex_gw_port:
            return
        floating_ips = (
                self.get_floating_ips() +
                self.get_port_forwarding_fips())
        device_name = self.get_external_device_interface_name(
            ex_gw_port)
        ipv4_rules_raw = []
        ipv6_rules_raw = []
        ipv6_rules_filter = []
        for fip in floating_ips:
            fip_address = fip['floating_ip_address']
            fip_type = fip.get('fip_type', 'floatingip')
            fip_denied_ports_list = fip.get('denied_port_numbers', [])
            if not fip_denied_ports_list:
                continue
            if fip_type != lib_constants.FLOATINGIP_TYPE_ECS_IPv6:
                for port_num in fip_denied_ports_list:
                    if netaddr.IPAddress(fip_address).version == 4:
                        rules = self.port_num_ipv4_rule(
                            device_name, fip_address, port_num)
                        for rule in rules:
                            ipv4_rules_raw.append(
                                ('denied-port', rule))
                    if (netaddr.IPAddress(fip_address).version == 6 and
                            self.agent_conf.enable_nat6to6):
                        rules = self.port_num_ipv6_rule(
                            device_name, fip_address, port_num)
                        for rule in rules:
                            ipv6_rules_raw.append(
                                ('denied-port', rule))
            else:
                if self.agent_conf.enable_nat6to6:
                    continue
                admin_state_up = fip.get('admin_state_up', False)
                if not admin_state_up:
                    continue
                for port_num in fip_denied_ports_list:
                    rules = self.port_num_ipv6_rule(
                        device_name, fip_address, port_num)
                    for rule in rules:
                        ipv6_rules_filter.append(
                            ('denied-port', rule))

        self._check_iptables_rules_exist(
            'raw', ipv4_rules_raw, result, ns_tables)
        self._check_iptables_rules_exist(
            'raw', ipv6_rules_raw, result, ns_tables, 'ipv6')
        self._check_iptables_rules_exist(
            'filter', ipv6_rules_filter, result, ns_tables, 'ipv6')

    def check_floating_ip_address_fip_admin_rules(
            self, result, ns_tables):
        ex_gw_port = self.get_ex_gw_port()
        if not ex_gw_port:
            return
        device_name = self.get_external_device_interface_name(
            ex_gw_port)
        floating_ips = (
                self.get_floating_ips() +
                self.get_port_forwarding_fips() +
                self.get_elastic_snat_fips())
        ipv4_rules_raw = []
        ipv4_rules_filter = []
        ipv6_rules_raw = []
        ipv6_rules_filter = []
        for fip in floating_ips:
            admin_state_up = fip.get('admin_state_up', True)
            if admin_state_up:
                continue
            floating_ip_type = fip.get('fip_type', 'floatingip')
            floating_ip_addr = fip['floating_ip_address']
            fixed_ip_address = fip['fixed_ip_address']
            if floating_ip_type == lib_constants.FLOATINGIP_TYPE_FIP:
                if netaddr.IPAddress(floating_ip_addr).version == 4:
                    ingress_rule = ('-d %s/32 -j DROP'
                                    % floating_ip_addr)
                    ipv4_rules_raw.append(('fip-admin', ingress_rule))
                    if not fixed_ip_address:
                        continue
                    egress_rule = ('-s %s/32 -o %s -j DROP' % (
                        fixed_ip_address, device_name))
                    ipv4_rules_filter.append(('fip-admin', egress_rule))
                elif (netaddr.IPAddress(floating_ip_addr).version == 6 and
                      self.agent_conf.enable_nat6to6):
                    ingress_rule = ('-d %s/128 -j DROP'
                                    % floating_ip_addr)
                    ipv6_rules_raw.append(('fip-admin', ingress_rule))
                    if not fixed_ip_address:
                        continue
                    egress_rule = ('-s %s/128 -o %s -j DROP' % (
                        fixed_ip_address, device_name))
                    ipv6_rules_filter.append(('fip-admin',
                                              egress_rule))
            elif (floating_ip_type ==
                  lib_constants.FLOATINGIP_TYPE_ECS_IPv6):
                if self.agent_conf.enable_nat6to6:
                    continue
                fip_admin_rules = self.ipv6_fip_admin_filter_rule(
                    device_name, floating_ip_addr)
                for rule in fip_admin_rules:
                    ipv6_rules_filter.append(('fip-admin', rule))

        self._check_iptables_rules_exist(
            'raw', ipv4_rules_raw, result, ns_tables)
        self._check_iptables_rules_exist(
            'filter', ipv4_rules_filter, result, ns_tables)
        self._check_iptables_rules_exist(
            'raw', ipv6_rules_raw, result, ns_tables, 'ipv6')
        self._check_iptables_rules_exist(
            'filter', ipv6_rules_filter, result, ns_tables, 'ipv6')

    def check_address_scope_iptables(self, result, ns_tables):
        # Check initialization rules for address scope iptables
        copy_address_scope_for_existing = (
            '-m connmark ! --mark 0x0/0xffff0000 '
            '-j CONNMARK --restore-mark '
            '--nfmask 0xffff0000 --ctmask 0xffff0000')
        save_mark_to_connmark = (
            '-m connmark --mark 0x0/0xffff0000 '
            '-j CONNMARK --save-mark '
            '--nfmask 0xffff0000 --ctmask 0xffff0000')
        ipv4_rules = [('PREROUTING', copy_address_scope_for_existing),
                 ('float-snat', save_mark_to_connmark)]
        self._check_iptables_rules_exist(
            'mangle', ipv4_rules, result, ns_tables)
        ipv6_rules = [('PREROUTING', copy_address_scope_for_existing)]
        self._check_iptables_rules_exist(
            'mangle', ipv6_rules, result, ns_tables, 'ipv6')

        # Check port address scope iptables rules
        ports_scopemark = self._get_address_scope_mark()
        external_device_name = None
        external_port = self.get_ex_gw_port()
        if external_port:
            external_device_name = self.get_external_device_name(
                external_port['id'])
        # Check address scope mark iptables rules
        for ip_version in (lib_constants.IP_VERSION_4,
                           lib_constants.IP_VERSION_6):
            scopemarks = ports_scopemark[ip_version]
            dont_block_external = \
                (ip_version == lib_constants.IP_VERSION_4 and
                 self._snat_enabled and external_port)
            rules = []
            for device_name, mark in scopemarks.items():
                rules.append(('scope',
                             self.address_scope_mangle_rule(
                                 device_name, mark)))
                self._check_iptables_rules_exist(
                    'mangle', rules, result,
                    ns_tables, 'ipv' + str(ip_version))
                rules.pop()
                if (dont_block_external and
                        device_name == external_device_name):
                    continue
                rules.append(('scope',
                             self.address_scope_filter_rule(
                                 device_name, mark)))
                self._check_iptables_rules_exist(
                    'filter', rules, result,
                    ns_tables, 'ipv' + str(ip_version))
                rules.pop()

        # Check pd address scope iptables rules
        for subnet_id, prefix in self.pd_subnets.items():
            if prefix != lib_constants.PROVISIONAL_IPV6_PD_PREFIX:
                self.check_pd_iptables_rules(prefix,
                                             result, ns_tables)

        self.check_floating_ip_address_scope_rules(result, ns_tables)

    def check_floating_ip_address_scope_rules(self,
                                              result,
                                              ns_tables):
        # Check floating ip address scope iptables rules
        floating_ips = self.get_floating_ips()
        ext_scope = self._get_external_address_scope()
        floating_ips = \
            [fip for fip in floating_ips
             if fip.get('fixed_ip_address_scope') != ext_scope]
        if floating_ips:
            ext_scope_mark = self.get_address_scope_mark_mask(
                ext_scope)
            ports_scopemark = self._get_address_scope_mark()
            devices_in_ext_scope = {
                device for device, mark
                in ports_scopemark[lib_constants.IP_VERSION_4].items()
                if mark == ext_scope_mark}
            # Check address scope for floatingip egress
            rules = []
            for device in devices_in_ext_scope:
                rules.append(
                    ('float-snat',
                     '-o %s -j MARK --set-xmark %s'
                     % (device, ext_scope_mark)))
                self._check_iptables_rules_exist(
                    'mangle', rules, result, ns_tables)
                rules.pop()
                if self.agent_conf.enable_nat6to6:
                    rules.append(
                        ('float-snat',
                         '-o %s -j MARK --set-xmark %s'
                         % (device, ext_scope_mark)))
                    self._check_iptables_rules_exist(
                        'mangle', rules, result, ns_tables, 'ipv6')
                    rules.pop()

        # Loop once to ensure that floating ips are configured.
        for fip in floating_ips:
            # Rebuild iptables rules for the floating ip.
            fip_ip = fip['floating_ip_address']
            addr = netaddr.IPAddress(fip_ip)
            # Send the floating ip traffic to the right address scope
            fixed_ip = fip['fixed_ip_address']

            # Filter out 'gatewayip', 'ecs ipv6'
            if not self.need_process_fip(fip):
                continue

            fixed_scope = fip.get('fixed_ip_address_scope')
            internal_mark = self.get_address_scope_mark_mask(
                fixed_scope)
            mangle_rules = self.floating_mangle_rules(
                fip_ip, fixed_ip, internal_mark)
            ipv4_rules = []
            ipv6_rules = []
            for chain, rule in mangle_rules:
                if addr.version == lib_constants.IP_VERSION_4:
                    ipv4_rules.append((chain, rule))
                elif self.agent_conf.enable_nat6to6:
                    ipv6_rules.append((chain, rule))
            self._check_iptables_rules_exist(
                'mangle', ipv4_rules, result, ns_tables)
            self._check_iptables_rules_exist(
                'mangle', ipv6_rules, result, ns_tables, 'ipv6')

    def check_pd_iptables_rules(self, prefix, result, ns_tables):
        ext_scope = self._get_external_address_scope()
        ext_scope_mark = self.get_address_scope_mark_mask(ext_scope)
        ex_gw_device = self.get_external_device_name(
            self.get_ex_gw_port()['id'])
        scope_rule = self.address_scope_mangle_rule(ex_gw_device,
                                                    ext_scope_mark)
        rules = [('scope', '-d %s ' % prefix + scope_rule)]
        self._check_iptables_rules_exist(
            'mangle', rules, result, ns_tables, 'ipv6')

    def check_floating_ip_iptables(self, result, ns_tables):
        floating_ips = self.get_floating_ips()
        # Loop once to ensure that floating ips are checked.
        for fip in floating_ips:
            # Filter out 'gatewayip', 'ecs ipv6' and using port_forwarding fips
            if not self.need_process_fip(fip):
                continue

            fip_ip = fip['floating_ip_address']
            addr = netaddr.IPAddress(fip_ip)
            if addr.version == lib_constants.IP_VERSION_4:
                rules = self.floating_forward_rules(fip)
                self._check_iptables_rules_exist('nat',
                                                 rules,
                                                 result,
                                                 ns_tables)
            elif self.agent_conf.enable_nat6to6:
                rules = self.floating_v6_forward_rules(fip)
                self._check_iptables_rules_exist('nat',
                                                 rules,
                                                 result,
                                                 ns_tables,
                                                 'ipv6')

    def check_esnat_rules(self, interface_name, rules, result, ns_tables):
        cidr_fip_pairs = []
        for rule in rules:
            for cidr in rule['internal_cidrs']:
                cidr_fip_pairs.append((cidr,
                                       rule['floating_ip_address']))
        for rule in sorted(
                cidr_fip_pairs,
                key=lambda rule: netaddr.IPNetwork(rule[0]).prefixlen,
                reverse=True):
            snat_rules = self.get_esnat_rule(
                interface_name,
                cidr=rule[0],
                snat_ip_addr=rule[1])

            self._check_iptables_rules_exist('nat', snat_rules,
                                             result, ns_tables)

    def check_port_forwarding_rules(self, result, ns_tables):
        pfips = self.router.get(
            n_const.PORT_FORWARDING_FLOATINGIP_KEY, [])
        for pfip in pfips:
            port_forwardings = pfip.get('port_forwardings', [])
            for port_forwarding in port_forwardings:
                pf_rules = self._get_pf_fip_rules(
                    port_forwarding, pfip)
                self._check_iptables_rules_exist('nat', pf_rules,
                                                 result, ns_tables)

    def get_esnat_rule(self, interface_name, cidr, snat_ip_addr):
        to_source = ('-s %(cidr)s -j SNAT -o %(gw_dev)s '
                     '--to-source %(gw_ip)s') % {
            "gw_dev": interface_name,
            "cidr": cidr,
            "gw_ip": snat_ip_addr}
        return [('esnat', to_source)]

    def _get_pf_fip_rules(self, port_forwarding, pfip):
        chain_rule_list = []
        port_forwarding_chain_prefix = 'pf-'
        chain_name = (port_forwarding_chain_prefix +
                      port_forwarding['id'])
        pf_chain_name = chain_name[
                        :n_const.MAX_IPTABLES_CHAIN_LEN_WRAP]
        floating_ip_address = pfip.get('floating_ip_address')
        protocol = port_forwarding.get('protocol')
        internal_ip_address = port_forwarding.get(
            'internal_ip_address')
        external_port, internal_port = self._extract_pf_ports(
            port_forwarding)

        chain_rule = (pf_chain_name,
                      '-d %s/32 -p %s -m %s --dport %s '
                      '-j DNAT --to-destination %s:%s' % (
                          floating_ip_address, protocol, protocol,
                          external_port, internal_ip_address,
                          internal_port))
        chain_rule_list.append(chain_rule)
        return chain_rule_list

    def _extract_pf_ports(self, port_forwarding):
        # The IP table rules handles internal port ranges with "-"
        # while the external ports ranges it handles using ":"
        internal_port = (port_forwarding.
                         get('internal_port_range').replace(':', '-'))
        external_port = port_forwarding.get('external_port_range')
        internal_start, internal_end = internal_port.split('-')
        external_start, external_end = external_port.split(':')
        internal_port_one_to_one_mask = ''
        is_single_external_port = external_start == external_end
        is_single_internal_port = internal_start == internal_end
        if is_single_external_port:
            external_port = external_start
        else:
            # This mask will ensure that the rules will be applied in
            # N-N like 40:50 -> 60:70, the port 40 will be mapped to
            # 60, the 41 to 61, 42 to 62...50 to 70.
            # In the case of 40:60 -> 70:80, the ports will be rounded
            # so the port 41 and 51, will be mapped to 71.
            internal_port_one_to_one_mask = '/' + external_start
        if is_single_internal_port:
            internal_port = internal_start
        else:
            internal_port = (internal_port +
                             internal_port_one_to_one_mask)

        return external_port, internal_port

    def _check_iptables_rules_exist(self, table, rules,
                                    result, ns_tables,
                                    ipversion='ipv4'):
        if not rules:
            return
        for chain, rule in rules:
            prefix = '-A %s-%s ' % (self.iptables_manager.wrap_name,
                                    chain)
            check_rule = prefix + rule
            if (self._normalize_rule(check_rule) not in
                    ns_tables[ipversion][table]):
                result['status'] = False
                result['iptables_error'].append(
                    "Missing fip iptables rule on %s chain"
                    " in %s table: %s."
                    % (chain, table, check_rule))

    def _normalize_rule(self, rule):
        parts = rule.split()
        return ' '.join(sorted(parts))

    def _normalize_ns_tables(self, ns_tables):
        normalized_tables = {
            'ipv4': {
                table: [] for table in ['filter', 'nat', 'mangle', 'raw']
            },
            'ipv6': {
                table: [] for table in ['filter', 'nat', 'mangle', 'raw']
            }
        }

        for ip_version, tables in ns_tables.items():
            for table, rules in tables.items():
                for rule in rules:
                    normalized_rule = self._normalize_rule(rule)
                    normalized_tables[ip_version][table].append(
                        normalized_rule)

        return normalized_tables

    def check_router_routes(self):
        result = {"status": True,
                  "route_error": [],
                  "device_route_error": []}
        if self.router_namespace.exists() is False:
            result['status'] = False
            return result
        router_routes = self.router['routes']
        ns_name = self.get_gw_ns_name()
        ip_wrapper = ip_lib.IPWrapper(namespace=ns_name)
        namespace_routes = self.get_ns_ip_route()
        ha = self.router.get('ha', False)

        # Check if expected routes are present in the namespace
        for route in router_routes:
            route_entry = "%s via %s" % (route['destination'],
                                         route['nexthop'])
            addr = netaddr.IPAddress(route['nexthop'])

            if addr.version == lib_constants.IP_VERSION_4:
                matches = [s for s in namespace_routes['ipv4']
                           if s.startswith(route_entry)]
                if len(matches) == 0:
                    result["status"] = False
                    result["route_error"].append(route)
            elif addr.version == lib_constants.IP_VERSION_6:
                matches = [s for s in namespace_routes['ipv6']
                           if s.startswith(route_entry)]
                if len(matches) == 0:
                    result["status"] = False
                    result["route_error"].append(route)

        # Get all devices excluding loopback
        devices = ip_wrapper.get_devices(exclude_loopback=True)
        qr_qg_ha_devices = []
        qg_device = None
        for device in devices:
            if (device.name.startswith(INTERNAL_DEV_PREFIX) or
                    device.name.startswith(HA_DEV_PREFIX)):
                qr_qg_ha_devices.append(device)
            elif device.name.startswith("qg-"):
                qr_qg_ha_devices.append(device)
                qg_device = device
        device_port_map = self._get_device_port_map(qr_qg_ha_devices,
                                                    ha)

        # Check link routes for each device
        for device in qr_qg_ha_devices:
            ports = device_port_map[device.name]
            if len(ports) == 0:
                result["status"] = False
                result["device_route_error"].append(
                    {"Device %s not found" % device.name})
                continue
            cidrs = self._extract_cidr_and_fixed_ips_from_port(ports)

            # Check HA virtual IP address route
            if device.name.startswith(HA_DEV_PREFIX) and ha:
                cidr, fixed_ip = self.get_vip_addresses()
                cidrs.append({'cidr': cidr, 'ip_address': fixed_ip})

            for cidr in cidrs:
                device_ip = cidr['ip_address']
                if netaddr.IPAddress(device_ip).version == 6:
                    continue
                device_route_entry = ("{} dev {} proto kernel scope"
                                      " link src {}").format(
                    cidr['cidr'], device.name, device_ip)
                if device_route_entry not in namespace_routes['ipv4']:
                    result["status"] = False
                    result["device_route_error"].append(
                        {"device": device.name,
                         "route": device_route_entry})

        # Check default route
        if self.ex_gw_port:
            for subnets in self.ex_gw_port['subnets']:
                gateway_ip = subnets['gateway_ip']
                if netaddr.IPAddress(gateway_ip).version == 4:
                    default_route = ("default via %s dev %s" %
                                     (gateway_ip, qg_device.name))

                    matches = [s for s in namespace_routes['ipv4']
                               if s.startswith(default_route)]

                    if len(matches) == 0:
                        result["status"] = False
                        result["route_error"].append(default_route)

        return result

    def _get_device_port_map(self, devices, ha):
        device_port_map = {}
        ports = []
        if self.internal_ports:
            ports.extend(self.internal_ports)
        if self.ex_gw_port:
            self.ex_gw_port = self.get_ex_gw_port()
            if self.ex_gw_port:
                ports.append(self.ex_gw_port)
        if ha and self.ha_port:
            ports.append(self.ha_port)

        for device in devices:
            device_name = device.name.replace(
                INTERNAL_DEV_PREFIX, '', 1)
            device_name = device_name.replace(
                EXTERNAL_DEV_PREFIX, '', 1)
            device_name = device_name.replace(
                HA_DEV_PREFIX, '', 1)
            if device_port_map.get(device_name) is None:
                device_port_map[device.name] = []

            for port in ports:
                if port['id'].startswith(device_name):
                    device_port_map[device.name].append(port)

        return device_port_map

    def _extract_cidr_and_fixed_ips_from_port(self, ports):
        cidrs = []
        for port_data in ports:
            for subnet in port_data['subnets']:
                subnet_id = subnet['id']
                cidr = subnet['cidr']
                fixed_ip = None
                for ip in port_data['fixed_ips']:
                    if ip['subnet_id'] == subnet_id:
                        fixed_ip = ip['ip_address']

                cidrs.append({'cidr': cidr, 'ip_address': fixed_ip})
        return cidrs

    def get_ns_devices(self):
        devices = self._get_existing_devices()
        ns_name = self.get_gw_ns_name()
        device_info_dict = {}
        for device in devices:
            ip_device = ip_lib.IPDevice(device, namespace=ns_name)
            ip_addresses = ip_device.addr.list()
            stats = ip_lib.get_device_stats(ns_name, device)
            device_info_dict[device] = {
                        'IP Address Configuration': ip_addresses,
                        'Interface Statistics': stats[0]
                    }

        return device_info_dict

    def get_ns_iptables_rule(self):
        tables = ['filter', 'nat', 'mangle', 'raw']
        rules_dict = {'ipv4': {}, 'ipv6': {}}
        for table in tables:
            # Get IPv4 rules
            ipv4_rules = (self.iptables_manager.
                          get_rules_for_table(table))
            rules_dict['ipv4'][table] = ipv4_rules

            # Get IPv6 rules
            ipv6_rules = (self.iptables_manager.
                          get_ipv6_rules_for_table(table))
            rules_dict['ipv6'][table] = ipv6_rules

        return rules_dict

    def get_ns_ip_route(self):
        ns_name = self.get_gw_ns_name()
        ip_wrapper = ip_lib.IPWrapper(namespace=ns_name)
        routes = {
            'ipv4': [],
            'ipv6': []
        }

        # Get IPv4 and IPv6 route table
        ipv4_rules = ip_wrapper.netns.execute(
            ['ip', 'rule'])
        ipv6_rules = ip_wrapper.netns.execute(
            ['ip', '-6', 'rule'])

        # Get IPv4 route for each table
        for line in ipv4_rules.splitlines():
            table_id = line.split()[-1]
            ipv4_routes = ip_wrapper.netns.execute(
                ['ip', 'route', 'show', 'table', table_id])
            routes['ipv4'].extend(
                [route.strip() for route in ipv4_routes.splitlines()])
        # Get IPv6 route for each table
        for line in ipv6_rules.splitlines():
            table_id = line.split()[-1]
            ipv6_routes = ip_wrapper.netns.execute(
                ['ip', '-6', 'route', 'show', 'table', table_id])
            routes['ipv6'].extend(
                [route.strip() for route in ipv6_routes.splitlines()])

        return routes
