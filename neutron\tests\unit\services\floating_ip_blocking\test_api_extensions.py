#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

"""
API and extension tests for Floating IP Blocking.
Tests API layer functionality, extensions, and end-to-end scenarios.
"""

import mock
from unittest import mock as umock

from neutron_lib import context
from neutron_lib.plugins import directory
from oslo_utils import uuidutils
from webob import exc as web_exc

from neutron.extensions import floating_ip_blocking
from neutron.services.floating_ip_blocking.common import exceptions as fip_exceptions
from neutron.tests import base


class TestFloatingIPBlockingAPI(base.BaseTestCase):
    """Test API layer functionality."""

    def setUp(self):
        super(TestFloatingIPBlockingAPI, self).setUp()
        self.context = context.get_admin_context()

    def test_api_exception_handling(self):
        """Test API exception handling and HTTP status codes."""
        # Test FloatingIPNotFound -> 404
        exception = fip_exceptions.FloatingIPNotFound(fip_id='test-id')
        status_code = fip_exceptions.get_http_status_code(exception)
        self.assertEqual(status_code, 404)
        
        # Test BlockingRuleConflict -> 409
        exception = fip_exceptions.BlockingRuleConflict(
            conflict_rule_id='test-rule', conflict_type='DUPLICATE', suggestion='test')
        status_code = fip_exceptions.get_http_status_code(exception)
        self.assertEqual(status_code, 409)
        
        # Test InvalidBlockingRule -> 400
        exception = fip_exceptions.InvalidBlockingRule(reason='test reason')
        status_code = fip_exceptions.get_http_status_code(exception)
        self.assertEqual(status_code, 400)

    def test_api_error_response_format(self):
        """Test API error response format consistency."""
        exception = fip_exceptions.FloatingIPNotFound(fip_id='test-id')
        error_response = fip_exceptions.format_error_response(exception)
        
        self.assertIn('error', error_response)
        self.assertIn('type', error_response['error'])
        self.assertIn('message', error_response['error'])
        self.assertIn('code', error_response['error'])
        
        self.assertEqual(error_response['error']['type'], 'FloatingIPNotFound')
        self.assertEqual(error_response['error']['code'], 404)

    def test_convert_exception_to_http_exc(self):
        """Test conversion of exceptions to HTTP exceptions."""
        # Test 404 conversion
        exception = fip_exceptions.FloatingIPNotFound(fip_id='test-id')
        http_exc = floating_ip_blocking.convert_exception_to_http_exc(exception)
        self.assertIsInstance(http_exc, web_exc.HTTPNotFound)
        
        # Test 409 conversion
        exception = fip_exceptions.BlockingRuleConflict(
            conflict_rule_id='test-rule', conflict_type='DUPLICATE', suggestion='test')
        http_exc = floating_ip_blocking.convert_exception_to_http_exc(exception)
        self.assertIsInstance(http_exc, web_exc.HTTPConflict)
        
        # Test 400 conversion
        exception = fip_exceptions.InvalidBlockingRule(reason='test reason')
        http_exc = floating_ip_blocking.convert_exception_to_http_exc(exception)
        self.assertIsInstance(http_exc, web_exc.HTTPBadRequest)

    def test_api_validation_functions(self):
        """Test API input validation functions."""
        # Test valid rule data
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress',
            'description': 'Test rule'
        }
        
        # Should not raise exception
        floating_ip_blocking.validate_blocking_rule(rule_data)
        
        # Test invalid rule data - missing direction
        invalid_rule_data = {
            'protocol': 'tcp',
            'local_port': 80
        }
        
        self.assertRaises(
            fip_exceptions.InvalidBlockingRule,
            floating_ip_blocking.validate_blocking_rule,
            invalid_rule_data
        )

    def test_rule_update_validation(self):
        """Test rule update validation."""
        original_rule = {
            'floatingip_id': 'test-fip-id',
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Valid update
        updated_rule = {
            'protocol': 'tcp',
            'local_port': 8080,
            'direction': 'ingress'
        }
        
        # Should not raise exception
        floating_ip_blocking.validate_rule_update(original_rule, updated_rule)
        
        # Invalid update - trying to change floatingip_id
        invalid_update = {
            'floatingip_id': 'different-fip-id',
            'protocol': 'tcp'
        }
        
        self.assertRaises(
            fip_exceptions.InvalidBlockingRule,
            floating_ip_blocking.validate_rule_update,
            original_rule, invalid_update
        )


class TestFloatingIPBlockingExtension(base.BaseTestCase):
    """Test extension functionality."""

    def setUp(self):
        super(TestFloatingIPBlockingExtension, self).setUp()
        self.extension = floating_ip_blocking.Floating_ip_blocking()

    def test_extension_properties(self):
        """Test extension basic properties."""
        self.assertEqual(self.extension.get_name(), 'Floating IP Traffic Blocking')
        self.assertEqual(self.extension.get_alias(), 'floating-ip-blocking')
        self.assertEqual(self.extension.get_description(), 
                        'Extension to control floating IP traffic blocking with fine-grained rules')

    def test_extension_resources(self):
        """Test extension resource registration."""
        resources = self.extension.get_resources()
        self.assertIsInstance(resources, list)
        self.assertGreater(len(resources), 0)
        
        # Check that blocking-rules resource is registered
        resource_names = [r.collection for r in resources]
        self.assertIn('blocking_rules', resource_names)

    def test_plugin_interface(self):
        """Test plugin interface definition."""
        plugin_interface = self.extension.get_plugin_interface()
        self.assertEqual(plugin_interface, floating_ip_blocking.FloatingIPBlockingPluginBase)


class TestFloatingIPBlockingPluginBase(base.BaseTestCase):
    """Test plugin base class functionality."""

    def setUp(self):
        super(TestFloatingIPBlockingPluginBase, self).setUp()
        
        # Create a concrete implementation for testing
        class TestPlugin(floating_ip_blocking.FloatingIPBlockingPluginBase):
            def create_floatingip_blocking_rule(self, context, floatingip_id, blocking_rule):
                return {'id': 'test-rule-id'}
            
            def update_floatingip_blocking_rule(self, context, id, floatingip_id, blocking_rule):
                return {'id': id}
            
            def get_floatingip_blocking_rule(self, context, id, floatingip_id, fields=None):
                return {'id': id}
            
            def get_floatingip_blocking_rules(self, context, floatingip_id=None, 
                                            filters=None, fields=None, sorts=None, 
                                            limit=None, marker=None, page_reverse=False):
                return [{'id': 'test-rule-id'}]
            
            def delete_floatingip_blocking_rule(self, context, id, floatingip_id):
                pass
        
        self.plugin = TestPlugin()

    def test_plugin_type(self):
        """Test plugin type identification."""
        plugin_type = self.plugin.get_plugin_type()
        self.assertEqual(plugin_type, 'FLOATING_IP_BLOCKING')

    def test_plugin_description(self):
        """Test plugin description."""
        description = self.plugin.get_plugin_description()
        self.assertEqual(description, "Floating IP Traffic Blocking Service Plugin")

    def test_abstract_methods_implementation(self):
        """Test that abstract methods are properly implemented."""
        context = mock.Mock()
        floatingip_id = 'test-fip-id'
        rule_id = 'test-rule-id'
        blocking_rule = {'protocol': 'tcp'}
        
        # Test create
        result = self.plugin.create_floatingip_blocking_rule(
            context, floatingip_id, blocking_rule)
        self.assertEqual(result['id'], 'test-rule-id')
        
        # Test update
        result = self.plugin.update_floatingip_blocking_rule(
            context, rule_id, floatingip_id, blocking_rule)
        self.assertEqual(result['id'], rule_id)
        
        # Test get single
        result = self.plugin.get_floatingip_blocking_rule(
            context, rule_id, floatingip_id)
        self.assertEqual(result['id'], rule_id)
        
        # Test get list
        result = self.plugin.get_floatingip_blocking_rules(
            context, floatingip_id)
        self.assertEqual(len(result), 1)
        self.assertEqual(result[0]['id'], 'test-rule-id')
        
        # Test delete (should not raise exception)
        self.plugin.delete_floatingip_blocking_rule(
            context, rule_id, floatingip_id)


class TestAPIDefinitions(base.BaseTestCase):
    """Test API definition constants and structures."""

    def test_api_constants(self):
        """Test API definition constants."""
        from neutron.extensions import _floating_ip_blocking as apidef
        
        self.assertEqual(apidef.NAME, 'Floating IP Traffic Blocking')
        self.assertEqual(apidef.ALIAS, 'floating-ip-blocking')
        self.assertEqual(apidef.RESOURCE_NAME, 'blocking_rule')
        self.assertEqual(apidef.COLLECTION_NAME, 'blocking_rules')
        self.assertEqual(apidef.API_PREFIX, '/floatingips')

    def test_protocol_choices(self):
        """Test protocol choices validation."""
        from neutron.extensions import _floating_ip_blocking as apidef
        
        expected_protocols = ['tcp', 'udp', 'icmp', 'any']
        self.assertEqual(apidef.PROTOCOL_CHOICES, expected_protocols)

    def test_direction_choices(self):
        """Test direction choices validation."""
        from neutron.extensions import _floating_ip_blocking as apidef
        
        expected_directions = ['ingress', 'egress']
        self.assertEqual(apidef.DIRECTION_CHOICES, expected_directions)

    def test_resource_attributes(self):
        """Test resource attribute definitions."""
        from neutron.extensions import _floating_ip_blocking as apidef
        
        # Check that blocking rule attributes are defined
        self.assertIn('id', apidef.BLOCKING_RULE_ATTRIBUTES)
        self.assertIn('protocol', apidef.BLOCKING_RULE_ATTRIBUTES)
        self.assertIn('local_port', apidef.BLOCKING_RULE_ATTRIBUTES)
        self.assertIn('remote_ip', apidef.BLOCKING_RULE_ATTRIBUTES)
        self.assertIn('remote_port', apidef.BLOCKING_RULE_ATTRIBUTES)
        self.assertIn('direction', apidef.BLOCKING_RULE_ATTRIBUTES)
        self.assertIn('description', apidef.BLOCKING_RULE_ATTRIBUTES)

    def test_sub_resource_structure(self):
        """Test sub-resource attribute map structure."""
        from neutron.extensions import _floating_ip_blocking as apidef
        
        self.assertIn(apidef.COLLECTION_NAME, apidef.SUB_RESOURCE_ATTRIBUTE_MAP)
        
        sub_resource = apidef.SUB_RESOURCE_ATTRIBUTE_MAP[apidef.COLLECTION_NAME]
        self.assertIn('parent', sub_resource)
        self.assertIn('parameters', sub_resource)
        
        parent = sub_resource['parent']
        self.assertEqual(parent['collection_name'], 'floatingips')
        self.assertEqual(parent['member_name'], 'floatingip')


class TestEndToEndScenarios(base.BaseTestCase):
    """Test end-to-end API scenarios."""

    def setUp(self):
        super(TestEndToEndScenarios, self).setUp()
        self.context = context.get_admin_context()
        self.floatingip_id = uuidutils.generate_uuid()

    def test_complete_rule_lifecycle(self):
        """Test complete rule lifecycle through API."""
        # This would be a more comprehensive test in a real implementation
        # For now, test the validation and exception handling flow
        
        # Test rule creation validation
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress',
            'description': 'Test HTTP blocking'
        }
        
        # Validate rule data
        floating_ip_blocking.validate_blocking_rule(rule_data)
        
        # Test rule update validation
        updated_rule_data = {
            'local_port': 8080,
            'description': 'Updated HTTP blocking'
        }
        
        original_rule = dict(rule_data)
        original_rule['floatingip_id'] = self.floatingip_id
        
        floating_ip_blocking.validate_rule_update(original_rule, updated_rule_data)

    def test_seven_blocking_modes_validation(self):
        """Test validation of all seven blocking modes."""
        # Mode 1: 1-tuple (direction only)
        mode1_rule = {'direction': 'ingress'}
        floating_ip_blocking.validate_blocking_rule(mode1_rule)
        
        # Mode 2: 2-tuple (direction + remote_ip)
        mode2_rule = {
            'remote_ip': '***********/24',
            'direction': 'ingress'
        }
        floating_ip_blocking.validate_blocking_rule(mode2_rule)
        
        # Mode 3A: 3-tuple (direction + protocol + local_port)
        mode3a_rule = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        floating_ip_blocking.validate_blocking_rule(mode3a_rule)
        
        # Mode 3B: 3-tuple (direction + protocol + remote_port)
        mode3b_rule = {
            'protocol': 'tcp',
            'remote_port': 8080,
            'direction': 'egress'
        }
        floating_ip_blocking.validate_blocking_rule(mode3b_rule)
        
        # Mode 4A: 4-tuple (direction + protocol + local_port + remote_ip)
        mode4a_rule = {
            'protocol': 'tcp',
            'local_port': 80,
            'remote_ip': '10.0.0.0/8',
            'direction': 'ingress'
        }
        floating_ip_blocking.validate_blocking_rule(mode4a_rule)
        
        # Mode 4B: 4-tuple (direction + protocol + remote_ip + remote_port)
        mode4b_rule = {
            'protocol': 'tcp',
            'remote_ip': '*************',
            'remote_port': 3306,
            'direction': 'egress'
        }
        floating_ip_blocking.validate_blocking_rule(mode4b_rule)
        
        # Mode 5: 5-tuple (complete)
        mode5_rule = {
            'protocol': 'tcp',
            'local_port': 443,
            'remote_ip': '***********/24',
            'remote_port': 8443,
            'direction': 'ingress'
        }
        floating_ip_blocking.validate_blocking_rule(mode5_rule)

    def test_error_scenarios(self):
        """Test various error scenarios."""
        # Test missing direction
        invalid_rule1 = {
            'protocol': 'tcp',
            'local_port': 80
        }
        self.assertRaises(
            fip_exceptions.InvalidBlockingRule,
            floating_ip_blocking.validate_blocking_rule,
            invalid_rule1
        )
        
        # Test invalid protocol
        invalid_rule2 = {
            'protocol': 'invalid_protocol',
            'direction': 'ingress'
        }
        self.assertRaises(
            fip_exceptions.InvalidBlockingRule,
            floating_ip_blocking.validate_blocking_rule,
            invalid_rule2
        )
        
        # Test ICMP with ports
        invalid_rule3 = {
            'protocol': 'icmp',
            'local_port': 80,
            'direction': 'ingress'
        }
        self.assertRaises(
            fip_exceptions.InvalidBlockingRule,
            floating_ip_blocking.validate_blocking_rule,
            invalid_rule3
        )

    def test_audit_logging(self):
        """Test audit logging functionality."""
        context = mock.Mock()
        context.user_id = 'test-user'
        context.project_id = 'test-project'
        
        # Test successful operation logging
        floating_ip_blocking.log_rule_operation(
            context, 'create', self.floatingip_id, 'test-rule-id',
            {'protocol': 'tcp', 'direction': 'ingress'}
        )
        
        # Test error logging
        error = fip_exceptions.InvalidBlockingRule(reason='test error')
        floating_ip_blocking.log_rule_operation(
            context, 'create', self.floatingip_id, error=error
        )


class TestAPIPerformance(base.BaseTestCase):
    """Test API performance scenarios."""

    def test_large_rule_set_validation(self):
        """Test validation performance with large rule sets."""
        # Test validation of many rules
        for i in range(100):
            rule_data = {
                'protocol': 'tcp',
                'local_port': 8000 + i,
                'direction': 'ingress',
                'description': f'Rule {i}'
            }
            floating_ip_blocking.validate_blocking_rule(rule_data)

    def test_complex_rule_validation(self):
        """Test validation of complex rules."""
        complex_rule = {
            'protocol': 'tcp',
            'local_port': 443,
            'remote_ip': '***********/24',
            'remote_port': 8443,
            'direction': 'ingress',
            'description': 'Complex HTTPS blocking rule with all parameters'
        }
        
        # Should handle complex rules efficiently
        floating_ip_blocking.validate_blocking_rule(complex_rule)

    def test_concurrent_validation(self):
        """Test concurrent validation scenarios."""
        # Simulate concurrent validation requests
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Multiple validations should not interfere with each other
        for _ in range(10):
            floating_ip_blocking.validate_blocking_rule(rule_data)