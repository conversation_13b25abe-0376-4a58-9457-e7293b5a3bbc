#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib import context
from neutron_lib import exceptions as n_exc
from oslo_utils import uuidutils
import sqlalchemy as sa
from sqlalchemy import exc as sql_exc

from neutron.db.models import floating_ip_blocking
from neutron.db.models import l3 as l3_models
from neutron.db import models_v2
from neutron.tests.unit import testlib_api


class FloatingIPBlockingDbTestCase(testlib_api.SqlTestCase):
    """Test case for FloatingIP blocking database models."""

    def setUp(self):
        super(FloatingIPBlockingDbTestCase, self).setUp()
        self.ctx = context.get_admin_context()

    def _create_floatingip(self, **kwargs):
        """Create a test floating IP."""
        defaults = {
            'id': uuidutils.generate_uuid(),
            'floating_ip_address': '*************',
            'floating_network_id': uuidutils.generate_uuid(),
            'floating_port_id': uuidutils.generate_uuid(),
            'project_id': uuidutils.generate_uuid(),
        }
        defaults.update(kwargs)
        
        fip = l3_models.FloatingIP(**defaults)
        self.ctx.session.add(fip)
        self.ctx.session.flush()
        return fip

    def _create_blocking_table(self, floatingip_id=None, **kwargs):
        """Create a test blocking table."""
        if not floatingip_id:
            fip = self._create_floatingip()
            floatingip_id = fip.id
            
        defaults = {
            'id': uuidutils.generate_uuid(),
            'floatingip_id': floatingip_id,
            'project_id': uuidutils.generate_uuid(),
            'enabled': True,
            'description': 'Test blocking table',
        }
        defaults.update(kwargs)
        
        table = floating_ip_blocking.FloatingIPBlockingTable(**defaults)
        self.ctx.session.add(table)
        self.ctx.session.flush()
        return table

    def _create_blocking_rule(self, blocking_table_id=None, **kwargs):
        """Create a test blocking rule."""
        if not blocking_table_id:
            table = self._create_blocking_table()
            blocking_table_id = table.id
            
        defaults = {
            'id': uuidutils.generate_uuid(),
            'blocking_table_id': blocking_table_id,
            'protocol': 'tcp',
            'local_port': 80,
            'remote_ip': '*************',
            'remote_port': None,
            'direction': 'ingress',
            'description': 'Test blocking rule',
        }
        defaults.update(kwargs)
        
        rule = floating_ip_blocking.FloatingIPBlockingRule(**defaults)
        self.ctx.session.add(rule)
        self.ctx.session.flush()
        return rule


class TestFloatingIPBlockingTable(FloatingIPBlockingDbTestCase):
    """Test FloatingIPBlockingTable model."""

    def test_create_blocking_table(self):
        """Test creating a blocking table."""
        fip = self._create_floatingip()
        table = self._create_blocking_table(floatingip_id=fip.id)
        
        self.assertEqual(fip.id, table.floatingip_id)
        self.assertTrue(table.enabled)
        self.assertEqual('Test blocking table', table.description)

    def test_blocking_table_floatingip_relationship(self):
        """Test the relationship between blocking table and floating IP."""
        fip = self._create_floatingip()
        table = self._create_blocking_table(floatingip_id=fip.id)
        
        # Test forward relationship
        self.assertEqual(fip, table.floatingip)
        
        # Test backward relationship
        self.assertEqual(table, fip.blocking_table)

    def test_unique_floatingip_constraint(self):
        """Test that only one blocking table can exist per floating IP."""
        fip = self._create_floatingip()
        self._create_blocking_table(floatingip_id=fip.id)
        
        # Try to create another blocking table for the same floating IP
        with self.assertRaises(sql_exc.IntegrityError):
            self._create_blocking_table(floatingip_id=fip.id)
            self.ctx.session.commit()

    def test_cascade_delete_on_floatingip_delete(self):
        """Test that blocking table is deleted when floating IP is deleted."""
        fip = self._create_floatingip()
        table = self._create_blocking_table(floatingip_id=fip.id)
        table_id = table.id
        
        # Delete the floating IP
        self.ctx.session.delete(fip)
        self.ctx.session.commit()
        
        # Verify blocking table is also deleted
        result = self.ctx.session.query(
            floating_ip_blocking.FloatingIPBlockingTable
        ).filter_by(id=table_id).first()
        self.assertIsNone(result)

    def test_blocking_table_fields_validation(self):
        """Test blocking table field constraints."""
        # Test enabled field default
        table = self._create_blocking_table(enabled=None)
        # Should default to True if not specified
        self.assertTrue(table.enabled)
        
        # Test description can be None
        table = self._create_blocking_table(description=None)
        self.assertIsNone(table.description)


class TestFloatingIPBlockingRule(FloatingIPBlockingDbTestCase):
    """Test FloatingIPBlockingRule model."""

    def test_create_blocking_rule(self):
        """Test creating a blocking rule."""
        table = self._create_blocking_table()
        rule = self._create_blocking_rule(blocking_table_id=table.id)
        
        self.assertEqual(table.id, rule.blocking_table_id)
        self.assertEqual('tcp', rule.protocol)
        self.assertEqual(80, rule.local_port)
        self.assertEqual('*************', rule.remote_ip)
        self.assertIsNone(rule.remote_port)
        self.assertEqual('ingress', rule.direction)

    def test_blocking_rule_table_relationship(self):
        """Test the relationship between blocking rule and blocking table."""
        table = self._create_blocking_table()
        rule = self._create_blocking_rule(blocking_table_id=table.id)
        
        # Test forward relationship
        self.assertEqual(table, rule.blocking_table)
        
        # Test backward relationship
        self.assertIn(rule, table.rules)

    def test_cascade_delete_on_table_delete(self):
        """Test that rules are deleted when blocking table is deleted."""
        table = self._create_blocking_table()
        rule = self._create_blocking_rule(blocking_table_id=table.id)
        rule_id = rule.id
        
        # Delete the blocking table
        self.ctx.session.delete(table)
        self.ctx.session.commit()
        
        # Verify rule is also deleted
        result = self.ctx.session.query(
            floating_ip_blocking.FloatingIPBlockingRule
        ).filter_by(id=rule_id).first()
        self.assertIsNone(result)

    def test_unique_rule_constraint(self):
        """Test that duplicate rules cannot be created."""
        table = self._create_blocking_table()
        self._create_blocking_rule(
            blocking_table_id=table.id,
            protocol='tcp',
            local_port=80,
            remote_ip='*************',
            remote_port=None,
            direction='ingress'
        )
        
        # Try to create the same rule again
        with self.assertRaises(sql_exc.IntegrityError):
            self._create_blocking_rule(
                blocking_table_id=table.id,
                protocol='tcp',
                local_port=80,
                remote_ip='*************',
                remote_port=None,
                direction='ingress'
            )
            self.ctx.session.commit()

    def test_different_directions_allowed(self):
        """Test that same rule with different directions is allowed."""
        table = self._create_blocking_table()
        
        # Create ingress rule
        rule1 = self._create_blocking_rule(
            blocking_table_id=table.id,
            protocol='tcp',
            local_port=80,
            remote_ip='*************',
            direction='ingress'
        )
        
        # Create egress rule with same parameters
        rule2 = self._create_blocking_rule(
            blocking_table_id=table.id,
            protocol='tcp',
            local_port=80,
            remote_ip='*************',
            direction='egress'
        )
        
        self.assertNotEqual(rule1.id, rule2.id)
        self.assertEqual('ingress', rule1.direction)
        self.assertEqual('egress', rule2.direction)

    def test_optional_fields(self):
        """Test that optional fields can be None."""
        table = self._create_blocking_table()
        
        # Create rule with minimal fields
        rule = self._create_blocking_rule(
            blocking_table_id=table.id,
            protocol=None,
            local_port=None,
            remote_ip=None,
            remote_port=None,
            direction='ingress',
            description=None
        )
        
        self.assertIsNone(rule.protocol)
        self.assertIsNone(rule.local_port)
        self.assertIsNone(rule.remote_ip)
        self.assertIsNone(rule.remote_port)
        self.assertIsNone(rule.description)
        self.assertEqual('ingress', rule.direction)

    def test_direction_enum_constraint(self):
        """Test that direction field only accepts valid values."""
        table = self._create_blocking_table()
        
        # Valid directions should work
        for direction in ['ingress', 'egress']:
            rule = self._create_blocking_rule(
                blocking_table_id=table.id,
                direction=direction
            )
            self.assertEqual(direction, rule.direction)

    def test_protocol_field_validation(self):
        """Test protocol field accepts various values."""
        table = self._create_blocking_table()
        
        # Test various protocol values
        for protocol in ['tcp', 'udp', 'icmp', 'any', None]:
            rule = self._create_blocking_rule(
                blocking_table_id=table.id,
                protocol=protocol
            )
            self.assertEqual(protocol, rule.protocol)

    def test_port_field_validation(self):
        """Test port fields accept integer values."""
        table = self._create_blocking_table()
        
        # Test valid port numbers
        for port in [80, 443, 22, 65535, None]:
            rule = self._create_blocking_rule(
                blocking_table_id=table.id,
                local_port=port,
                remote_port=port
            )
            self.assertEqual(port, rule.local_port)
            self.assertEqual(port, rule.remote_port)

    def test_remote_ip_field_validation(self):
        """Test remote_ip field accepts IP addresses and CIDR."""
        table = self._create_blocking_table()
        
        # Test various IP formats
        ip_values = [
            '*************',
            '10.0.0.0/24',
            '2001:db8::1',
            '2001:db8::/32',
            None
        ]
        
        for ip in ip_values:
            rule = self._create_blocking_rule(
                blocking_table_id=table.id,
                remote_ip=ip
            )
            self.assertEqual(ip, rule.remote_ip)


class TestFloatingIPBlockingIntegration(FloatingIPBlockingDbTestCase):
    """Test integration between FloatingIP and blocking models."""

    def test_floatingip_with_blocking_table_and_rules(self):
        """Test complete integration with floating IP, table, and rules."""
        # Create floating IP
        fip = self._create_floatingip()
        
        # Create blocking table
        table = self._create_blocking_table(floatingip_id=fip.id)
        
        # Create multiple rules
        rule1 = self._create_blocking_rule(
            blocking_table_id=table.id,
            protocol='tcp',
            local_port=80,
            direction='ingress'
        )
        
        rule2 = self._create_blocking_rule(
            blocking_table_id=table.id,
            protocol='tcp',
            local_port=443,
            direction='ingress'
        )
        
        # Verify relationships
        self.assertEqual(fip, table.floatingip)
        self.assertEqual(table, fip.blocking_table)
        self.assertEqual(2, len(table.rules))
        self.assertIn(rule1, table.rules)
        self.assertIn(rule2, table.rules)

    def test_cascade_delete_full_chain(self):
        """Test cascade delete from floating IP down to rules."""
        # Create the full chain
        fip = self._create_floatingip()
        table = self._create_blocking_table(floatingip_id=fip.id)
        rule1 = self._create_blocking_rule(blocking_table_id=table.id)
        rule2 = self._create_blocking_rule(
            blocking_table_id=table.id,
            local_port=443
        )
        
        table_id = table.id
        rule1_id = rule1.id
        rule2_id = rule2.id
        
        # Delete floating IP
        self.ctx.session.delete(fip)
        self.ctx.session.commit()
        
        # Verify everything is deleted
        self.assertIsNone(
            self.ctx.session.query(
                floating_ip_blocking.FloatingIPBlockingTable
            ).filter_by(id=table_id).first()
        )
        
        self.assertIsNone(
            self.ctx.session.query(
                floating_ip_blocking.FloatingIPBlockingRule
            ).filter_by(id=rule1_id).first()
        )
        
        self.assertIsNone(
            self.ctx.session.query(
                floating_ip_blocking.FloatingIPBlockingRule
            ).filter_by(id=rule2_id).first()
        )