{"blocking_rules_list_response": {"blocking_rules": [{"id": "f47ac10b-58cc-4372-a567-0e02b2c3d479", "floatingip_id": "2f245a7b-796b-4f26-9cf9-9e82d248fda7", "protocol": "tcp", "local_port": 80, "remote_ip": "*************", "remote_port": null, "direction": "ingress", "description": "Block HTTP access from specific IP", "created_at": "2025-01-19T10:00:00Z", "updated_at": "2025-01-19T10:00:00Z"}, {"id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8", "floatingip_id": "2f245a7b-796b-4f26-9cf9-9e82d248fda7", "protocol": "tcp", "local_port": 443, "remote_ip": "***********/24", "remote_port": null, "direction": "ingress", "description": "Block HTTPS access from subnet", "created_at": "2025-01-19T10:05:00Z", "updated_at": "2025-01-19T10:05:00Z"}]}, "blocking_rule_create_request": {"blocking_rule": {"protocol": "tcp", "local_port": 22, "remote_ip": "10.0.0.0/8", "direction": "ingress", "description": "Block SSH access from private networks"}}, "blocking_rule_create_response": {"blocking_rule": {"id": "f47ac10b-58cc-4372-a567-0e02b2c3d479", "floatingip_id": "2f245a7b-796b-4f26-9cf9-9e82d248fda7", "protocol": "tcp", "local_port": 22, "remote_ip": "10.0.0.0/8", "remote_port": null, "direction": "ingress", "description": "Block SSH access from private networks", "created_at": "2025-01-19T10:00:00Z", "updated_at": "2025-01-19T10:00:00Z"}}, "blocking_rule_update_request": {"blocking_rule": {"protocol": "tcp", "local_port": 8080, "remote_ip": "***********/24", "direction": "ingress", "description": "Block HTTP access from entire subnet"}}, "blocking_rule_update_response": {"blocking_rule": {"id": "f47ac10b-58cc-4372-a567-0e02b2c3d479", "floatingip_id": "2f245a7b-796b-4f26-9cf9-9e82d248fda7", "protocol": "tcp", "local_port": 8080, "remote_ip": "***********/24", "remote_port": null, "direction": "ingress", "description": "Block HTTP access from entire subnet", "created_at": "2025-01-19T10:00:00Z", "updated_at": "2025-01-19T10:15:00Z"}}, "seven_blocking_modes_examples": {"1_tuple_fip_only": {"blocking_rule": {"direction": "ingress", "description": "Block all ingress traffic to this floating IP"}}, "2_tuple_fip_remote_ip": {"blocking_rule": {"remote_ip": "***********/24", "direction": "ingress", "description": "Block all traffic from specific subnet"}}, "3_tuple_a_fip_protocol_local_port": {"blocking_rule": {"protocol": "tcp", "local_port": 80, "direction": "ingress", "description": "Block HTTP traffic to this floating IP"}}, "3_tuple_b_fip_protocol_remote_port": {"blocking_rule": {"protocol": "tcp", "remote_port": 8080, "direction": "egress", "description": "Block outbound traffic to port 8080"}}, "4_tuple_a_fip_protocol_local_port_remote_ip": {"blocking_rule": {"protocol": "tcp", "local_port": 80, "remote_ip": "10.0.0.0/8", "direction": "ingress", "description": "Block HTTP from private networks"}}, "4_tuple_b_fip_protocol_remote_ip_remote_port": {"blocking_rule": {"protocol": "tcp", "remote_ip": "*************", "remote_port": 3306, "direction": "egress", "description": "Block MySQL access to specific server"}}, "5_tuple_complete": {"blocking_rule": {"protocol": "tcp", "local_port": 443, "remote_ip": "***********/24", "remote_port": 8443, "direction": "ingress", "description": "Block specific HTTPS connection"}}}, "error_responses": {"floating_ip_not_found": {"error": {"type": "FloatingIPNotFound", "message": "Floating IP 2f245a7b-796b-4f26-9cf9-9e82d248fda7 not found", "code": 404}}, "blocking_rule_conflict": {"error": {"type": "BlockingRuleConflict", "message": "Blocking rule conflicts with existing rule 6ba7b810-9dad-11d1-80b4-00c04fd430c8", "code": 409, "details": {"conflict_type": "DUPLICATE", "existing_rule_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8", "suggestion": "Rule with identical parameters already exists"}}}, "invalid_blocking_rule": {"error": {"type": "InvalidBlockingRule", "message": "Invalid blocking rule: port number must be between 1 and 65535", "code": 400}}}}