#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

"""
Rule validation and conflict detection for floating IP blocking.
Extracted from main plugin to improve maintainability.
"""

import ipaddress

from oslo_log import log as logging

from neutron.services.floating_ip_blocking.common import exceptions as fip_blocking_exc

LOG = logging.getLogger(__name__)


class RuleValidator:
    """Handles rule validation and conflict detection."""

    def validate_rule_data(self, rule_data):
        """Validate rule data - supports seven blocking modes."""
        # Validate protocol
        protocol = rule_data.get('protocol')
        if protocol and protocol not in ['tcp', 'udp', 'icmp', 'any']:
            raise fip_blocking_exc.InvalidBlockingRule(
                reason=f"Invalid protocol: {protocol}")

        # Validate port ranges
        for port_field in ['local_port', 'remote_port']:
            port = rule_data.get(port_field)
            if port is not None:
                if not isinstance(port, int) or port < 1 or port > 65535:
                    raise fip_blocking_exc.InvalidBlockingRule(
                        reason=f"Invalid {port_field}: {port}")

        # Validate IP address (supports CIDR)
        remote_ip = rule_data.get('remote_ip')
        if remote_ip:
            try:
                ipaddress.ip_network(remote_ip, strict=False)
            except ValueError:
                raise fip_blocking_exc.InvalidBlockingRule(
                    reason=f"Invalid remote_ip: {remote_ip}")

        # Validate direction
        direction = rule_data.get('direction')
        if direction not in ['ingress', 'egress']:
            raise fip_blocking_exc.InvalidBlockingRule(
                reason=f"Invalid direction: {direction}")

        # Validate seven blocking modes logic consistency
        self._validate_blocking_mode(rule_data)

    def _validate_blocking_mode(self, rule_data):
        """Validate seven blocking modes logic consistency."""
        protocol = rule_data.get('protocol')
        local_port = rule_data.get('local_port')
        remote_port = rule_data.get('remote_port')

        # Port fields need protocol support
        if (local_port or remote_port) and not protocol:
            raise fip_blocking_exc.BlockingRuleValidationError(
                reason="Port fields require protocol to be specified")

        # ICMP protocol doesn't support ports
        if protocol == 'icmp' and (local_port or remote_port):
            raise fip_blocking_exc.BlockingRuleValidationError(
                reason="ICMP protocol does not support port fields")

        # Validate mode completeness
        mode = self.identify_blocking_mode(rule_data)
        LOG.debug(f"Identified blocking mode: {mode}")

    def identify_blocking_mode(self, rule_data):
        """Identify blocking mode type."""
        protocol = rule_data.get('protocol')
        local_port = rule_data.get('local_port')
        remote_ip = rule_data.get('remote_ip')
        remote_port = rule_data.get('remote_port')

        # Count non-empty fields (excluding direction and description)
        fields = [protocol, local_port, remote_ip, remote_port]
        non_empty_count = sum(1 for field in fields if field is not None)

        if non_empty_count == 0:
            return "1-tuple (FIP only)"
        elif non_empty_count == 1:
            if remote_ip:
                return "2-tuple (FIP + Remote IP)"
            elif protocol and local_port:
                return "3-tuple A (FIP + Protocol + Local Port)"
            elif protocol and remote_port:
                return "3-tuple B (FIP + Protocol + Remote Port)"
            else:
                return "Partial tuple"
        elif non_empty_count == 2:
            if protocol and local_port and not remote_ip and not remote_port:
                return "3-tuple A (FIP + Protocol + Local Port)"
            elif protocol and remote_port and not local_port and not remote_ip:
                return "3-tuple B (FIP + Protocol + Remote Port)"
            elif remote_ip and not protocol and not local_port and not remote_port:
                return "2-tuple (FIP + Remote IP)"
            else:
                return "Mixed 2-tuple"
        elif non_empty_count == 3:
            if protocol and local_port and remote_ip:
                return "4-tuple A (FIP + Protocol + Local Port + Remote IP)"
            elif protocol and remote_ip and remote_port:
                return "4-tuple B (FIP + Protocol + Remote IP + Remote Port)"
            else:
                return "Mixed 3-tuple"
        elif non_empty_count == 4:
            return "5-tuple (Complete)"
        else:
            return "Unknown"


class ConflictDetector:
    """Handles rule conflict detection and analysis."""

    def __init__(self, rule_validator):
        self.rule_validator = rule_validator

    def detect_rule_conflicts(self, context, existing_rules, new_rule_data, exclude_rule_id=None):
        """Detect rule conflicts - enhanced five-tuple overlap detection algorithm."""
        if exclude_rule_id:
            existing_rules = [r for r in existing_rules if r.id != exclude_rule_id]

        conflicts = []
        for rule in existing_rules:
            overlap_result = self._analyze_rule_overlap(new_rule_data, rule)
            if overlap_result['overlaps']:
                conflicts.append({
                    'rule_id': rule.id,
                    'conflict_type': overlap_result['conflict_type'],
                    'overlap_degree': overlap_result['overlap_degree'],
                    'resolution_suggestion': self._suggest_resolution(new_rule_data, rule),
                    'existing_rule_mode': self.rule_validator.identify_blocking_mode(self._rule_to_dict(rule)),
                    'new_rule_mode': self.rule_validator.identify_blocking_mode(new_rule_data)
                })
        return conflicts

    def _analyze_rule_overlap(self, rule1, rule2):
        """Analyze overlap between two rules."""
        # Check each five-tuple field overlap
        overlap_analysis = {
            'protocol': self._field_overlap_analysis(
                self._get_rule_field(rule1, 'protocol'),
                self._get_rule_field(rule2, 'protocol')
            ),
            'local_port': self._field_overlap_analysis(
                self._get_rule_field(rule1, 'local_port'),
                self._get_rule_field(rule2, 'local_port')
            ),
            'remote_ip': self._ip_field_overlap_analysis(
                self._get_rule_field(rule1, 'remote_ip'),
                self._get_rule_field(rule2, 'remote_ip')
            ),
            'remote_port': self._field_overlap_analysis(
                self._get_rule_field(rule1, 'remote_port'),
                self._get_rule_field(rule2, 'remote_port')
            ),
            'direction': self._field_overlap_analysis(
                self._get_rule_field(rule1, 'direction'),
                self._get_rule_field(rule2, 'direction')
            )
        }

        # Determine overall overlap
        overlaps = all(analysis['overlaps'] for analysis in overlap_analysis.values())
        
        if not overlaps:
            return {'overlaps': False, 'conflict_type': 'NONE', 'overlap_degree': 0}

        # Calculate overlap degree and conflict type
        overlap_degree = sum(1 for analysis in overlap_analysis.values() 
                           if analysis['relationship'] == 'IDENTICAL')
        
        conflict_type = self._determine_conflict_type(overlap_analysis)
        
        return {
            'overlaps': True,
            'conflict_type': conflict_type,
            'overlap_degree': overlap_degree,
            'field_analysis': overlap_analysis
        }

    def _field_overlap_analysis(self, field1, field2):
        """Analyze single field overlap."""
        if field1 is None and field2 is None:
            return {'overlaps': True, 'relationship': 'BOTH_WILDCARD'}
        elif field1 is None:
            return {'overlaps': True, 'relationship': 'FIELD1_SUBSUMES'}
        elif field2 is None:
            return {'overlaps': True, 'relationship': 'FIELD2_SUBSUMES'}
        elif field1 == field2:
            return {'overlaps': True, 'relationship': 'IDENTICAL'}
        else:
            return {'overlaps': False, 'relationship': 'DISJOINT'}

    def _ip_field_overlap_analysis(self, ip1, ip2):
        """Analyze IP field overlap (supports CIDR)."""
        if ip1 is None and ip2 is None:
            return {'overlaps': True, 'relationship': 'BOTH_WILDCARD'}
        elif ip1 is None:
            return {'overlaps': True, 'relationship': 'FIELD1_SUBSUMES'}
        elif ip2 is None:
            return {'overlaps': True, 'relationship': 'FIELD2_SUBSUMES'}
        
        try:
            net1 = ipaddress.ip_network(ip1, strict=False)
            net2 = ipaddress.ip_network(ip2, strict=False)
            
            if net1 == net2:
                return {'overlaps': True, 'relationship': 'IDENTICAL'}
            elif net1.supernet_of(net2):
                return {'overlaps': True, 'relationship': 'FIELD1_SUBSUMES'}
            elif net2.supernet_of(net1):
                return {'overlaps': True, 'relationship': 'FIELD2_SUBSUMES'}
            elif net1.overlaps(net2):
                return {'overlaps': True, 'relationship': 'PARTIAL_OVERLAP'}
            else:
                return {'overlaps': False, 'relationship': 'DISJOINT'}
        except ValueError:
            # If not valid network addresses, compare as strings
            if ip1 == ip2:
                return {'overlaps': True, 'relationship': 'IDENTICAL'}
            else:
                return {'overlaps': False, 'relationship': 'DISJOINT'}

    def _determine_conflict_type(self, overlap_analysis):
        """Determine conflict type based on field overlap analysis."""
        relationships = [analysis['relationship'] for analysis in overlap_analysis.values()]
        
        if all(rel == 'IDENTICAL' for rel in relationships):
            return 'DUPLICATE'
        elif any(rel == 'FIELD1_SUBSUMES' or rel == 'BOTH_WILDCARD' for rel in relationships):
            if not any(rel == 'FIELD2_SUBSUMES' for rel in relationships):
                return 'NEW_RULE_SUBSUMES'
        elif any(rel == 'FIELD2_SUBSUMES' for rel in relationships):
            return 'EXISTING_RULE_SUBSUMES'
        elif any(rel == 'PARTIAL_OVERLAP' for rel in relationships):
            return 'PARTIAL_OVERLAP'
        
        return 'COMPLEX_OVERLAP'

    def _get_rule_field(self, rule, field_name):
        """Get rule field value."""
        if hasattr(rule, field_name):
            return getattr(rule, field_name)
        else:
            return rule.get(field_name)

    def _rule_to_dict(self, rule_obj):
        """Convert rule object to dictionary."""
        return {
            'protocol': rule_obj.protocol,
            'local_port': rule_obj.local_port,
            'remote_ip': rule_obj.remote_ip,
            'remote_port': rule_obj.remote_port,
            'direction': rule_obj.direction,
            'description': rule_obj.description
        }

    def _suggest_resolution(self, rule1, rule2):
        """Suggest conflict resolution - enhanced version."""
        overlap_result = self._analyze_rule_overlap(rule1, rule2)
        rule2_id = getattr(rule2, 'id', 'unknown')
        conflict_type = overlap_result['conflict_type']

        suggestions = {
            'DUPLICATE': "规则完全重复，建议删除其中一个规则",
            'NEW_RULE_SUBSUMES': f"新规则范围更广，包含现有规则 {rule2_id}。建议：1) 删除现有规则，或 2) 缩小新规则范围",
            'EXISTING_RULE_SUBSUMES': f"现有规则 {rule2_id} 范围更广，包含新规则。建议：1) 使用现有规则，或 2) 扩展现有规则描述",
            'PARTIAL_OVERLAP': f"规则与现有规则 {rule2_id} 部分重叠。建议：1) 调整IP范围避免重叠，或 2) 合并为单个规则",
            'COMPLEX_OVERLAP': f"规则与现有规则 {rule2_id} 存在复杂重叠。建议详细检查五元组字段并调整"
        }

        base_suggestion = suggestions.get(conflict_type, f"规则与现有规则 {rule2_id} 存在冲突")
        
        # Add specific field suggestions
        if 'field_analysis' in overlap_result:
            field_suggestions = []
            for field, analysis in overlap_result['field_analysis'].items():
                if analysis['relationship'] in ['FIELD1_SUBSUMES', 'FIELD2_SUBSUMES', 'PARTIAL_OVERLAP']:
                    field_suggestions.append(f"调整{field}字段")
            
            if field_suggestions:
                base_suggestion += f"。具体建议：{', '.join(field_suggestions)}"

        return base_suggestion