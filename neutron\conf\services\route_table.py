#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

from oslo_config import cfg

from neutron._i18n import _


ROUTE_TABLE_OPTS = [
    cfg.IntOpt('max_route_tables',
               default=10,
               help=_('Max route tables one vpc can have.')),
    cfg.IntOpt('max_route_table_routes',
               default=200,
               help=_('Max routes for route table.')),
    cfg.ListOpt('supported_routes_nexthop_type',
                default=['ecs', 'ipv4', 'default', 'system_direct'],
                help=_('Supported nexthop types.'))
]


def register_route_table_opts(conf=cfg.CONF):
    conf.register_opts(ROUTE_TABLE_OPTS)
