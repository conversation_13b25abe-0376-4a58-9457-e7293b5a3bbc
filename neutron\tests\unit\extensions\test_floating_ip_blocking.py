#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import copy
import mock

from neutron_lib import context
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import directory
from oslo_utils import uuidutils
from webob import exc

from neutron.extensions import floating_ip_blocking
from neutron.extensions import _floating_ip_blocking as apidef
from neutron.tests.unit.api import test_extensions
from neutron.tests.unit.extensions import test_l3

_uuid = uuidutils.generate_uuid


class FloatingIPBlockingPluginStub(object):
    """Stub plugin for testing floating IP blocking extension."""
    
    def __init__(self):
        self.blocking_rules = {}
        self.floatingips = {}
        # Mock some floating IPs for testing
        self.floatingips = {
            'fip-1': {'id': 'fip-1', 'project_id': 'project-1'},
            'fip-2': {'id': 'fip-2', 'project_id': 'project-2'},
        }
    
    def get_plugin_type(self):
        return apidef.FLOATING_IP_BLOCKING
    
    def get_plugin_description(self):
        return "Test Floating IP Blocking Plugin"
    
    def create_floatingip_blocking_rule(self, context, floatingip_id, 
                                       blocking_rule):
        # Check if floating IP exists and user has access
        if floatingip_id not in self.floatingips:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        fip = self.floatingips[floatingip_id]
        if context.project_id != fip['project_id'] and not context.is_admin:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        rule_data = blocking_rule['blocking_rule']
        rule_id = _uuid()
        rule = {
            'id': rule_id,
            'floatingip_id': floatingip_id,
            'project_id': context.project_id,
            'protocol': rule_data.get('protocol'),
            'local_port': rule_data.get('local_port'),
            'remote_ip': rule_data.get('remote_ip'),
            'remote_port': rule_data.get('remote_port'),
            'direction': rule_data.get('direction'),
            'description': rule_data.get('description', ''),
            'created_at': '2025-01-19T10:00:00Z',
            'updated_at': '2025-01-19T10:00:00Z'
        }
        
        # Validate rule
        floating_ip_blocking.validate_blocking_rule(rule_data)
        
        # Check for conflicts (simplified)
        if floatingip_id in self.blocking_rules:
            for existing_rule in self.blocking_rules[floatingip_id].values():
                if self._rules_conflict(rule, existing_rule):
                    raise floating_ip_blocking.BlockingRuleConflict(
                        details=f"Conflicts with rule {existing_rule['id']}")
        
        # Store rule
        if floatingip_id not in self.blocking_rules:
            self.blocking_rules[floatingip_id] = {}
        self.blocking_rules[floatingip_id][rule_id] = rule
        
        return rule
    
    def update_floatingip_blocking_rule(self, context, id, floatingip_id, 
                                       blocking_rule):
        # Check if floating IP exists and user has access
        if floatingip_id not in self.floatingips:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        fip = self.floatingips[floatingip_id]
        if context.project_id != fip['project_id'] and not context.is_admin:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        if (floatingip_id not in self.blocking_rules or 
            id not in self.blocking_rules[floatingip_id]):
            raise floating_ip_blocking.BlockingRuleNotFound(rule_id=id)
        
        original_rule = self.blocking_rules[floatingip_id][id]
        rule_data = blocking_rule['blocking_rule']
        
        # Validate update
        floating_ip_blocking.validate_rule_update(original_rule, rule_data)
        
        # Update rule
        updated_rule = original_rule.copy()
        updated_rule.update(rule_data)
        updated_rule['updated_at'] = '2025-01-19T11:00:00Z'
        
        self.blocking_rules[floatingip_id][id] = updated_rule
        return updated_rule
    
    def get_floatingip_blocking_rule(self, context, id, floatingip_id, 
                                    fields=None):
        # Check if floating IP exists and user has access
        if floatingip_id not in self.floatingips:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        fip = self.floatingips[floatingip_id]
        if context.project_id != fip['project_id'] and not context.is_admin:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        if (floatingip_id not in self.blocking_rules or 
            id not in self.blocking_rules[floatingip_id]):
            raise floating_ip_blocking.BlockingRuleNotFound(rule_id=id)
        
        rule = self.blocking_rules[floatingip_id][id]
        if fields:
            return {field: rule.get(field) for field in fields}
        return rule
    
    def get_floatingip_blocking_rules(self, context, floatingip_id=None, 
                                     filters=None, fields=None, sorts=None, 
                                     limit=None, marker=None, 
                                     page_reverse=False):
        # Check if floating IP exists and user has access
        if floatingip_id not in self.floatingips:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        fip = self.floatingips[floatingip_id]
        if context.project_id != fip['project_id'] and not context.is_admin:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        if floatingip_id not in self.blocking_rules:
            return []
        
        rules = list(self.blocking_rules[floatingip_id].values())
        
        # Apply filters if provided
        if filters:
            filtered_rules = []
            for rule in rules:
                match = True
                for key, values in filters.items():
                    if rule.get(key) not in values:
                        match = False
                        break
                if match:
                    filtered_rules.append(rule)
            rules = filtered_rules
        
        # Apply field selection if provided
        if fields:
            rules = [{field: rule.get(field) for field in fields} 
                    for rule in rules]
        
        return rules
    
    def delete_floatingip_blocking_rule(self, context, id, floatingip_id):
        # Check if floating IP exists and user has access
        if floatingip_id not in self.floatingips:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        fip = self.floatingips[floatingip_id]
        if context.project_id != fip['project_id'] and not context.is_admin:
            raise floating_ip_blocking.FloatingIPNotFound(
                floatingip_id=floatingip_id)
        
        if (floatingip_id not in self.blocking_rules or 
            id not in self.blocking_rules[floatingip_id]):
            raise floating_ip_blocking.BlockingRuleNotFound(rule_id=id)
        
        del self.blocking_rules[floatingip_id][id]
        
        # Clean up empty dict
        if not self.blocking_rules[floatingip_id]:
            del self.blocking_rules[floatingip_id]
    
    def _rules_conflict(self, rule1, rule2):
        """Simple conflict detection for testing."""
        # Check if rules have identical five-tuple and direction
        return (rule1.get('protocol') == rule2.get('protocol') and
                rule1.get('local_port') == rule2.get('local_port') and
                rule1.get('remote_ip') == rule2.get('remote_ip') and
                rule1.get('remote_port') == rule2.get('remote_port') and
                rule1.get('direction') == rule2.get('direction'))


class FloatingIPBlockingExtensionManager(object):
    """Extension manager for floating IP blocking tests."""
    
    def get_resources(self):
        return floating_ip_blocking.Floating_ip_blocking.get_resources()
    
    def get_actions(self):
        return []
    
    def get_request_extensions(self):
        return []


class FloatingIPBlockingExtensionTestCase(test_extensions.ExtensionTestCase):
    """Test case for floating IP blocking extension."""
    
    def setUp(self):
        super(FloatingIPBlockingExtensionTestCase, self).setUp()
        self.plugin = FloatingIPBlockingPluginStub()
        
        # Mock the plugin directory
        directory.add_plugin(apidef.FLOATING_IP_BLOCKING, self.plugin)
        
        ext_mgr = FloatingIPBlockingExtensionManager()
        self.ext_api = test_extensions.setup_extensions_middleware(ext_mgr)
    
    def tearDown(self):
        super(FloatingIPBlockingExtensionTestCase, self).tearDown()
        directory._PLUGINS = {}


class TestFloatingIPBlockingAPI(FloatingIPBlockingExtensionTestCase):
    """Test floating IP blocking API endpoints."""
    
    def setUp(self):
        super(TestFloatingIPBlockingAPI, self).setUp()
        self.floatingip_id = 'fip-1'
        self.project_id = 'project-1'
        self.context = context.Context('user-1', self.project_id)
    
    def _create_blocking_rule(self, floatingip_id=None, rule_data=None):
        """Helper method to create a blocking rule."""
        if floatingip_id is None:
            floatingip_id = self.floatingip_id
        
        if rule_data is None:
            rule_data = {
                'protocol': 'tcp',
                'local_port': 80,
                'direction': 'ingress',
                'description': 'Test rule'
            }
        
        data = {'blocking_rule': rule_data}
        req = self.new_create_request(
            f'floatingips/{floatingip_id}/blocking-rules',
            data, self.fmt)
        req.environ['neutron.context'] = self.context
        return req.get_response(self.ext_api)
    
    def _update_blocking_rule(self, floatingip_id, rule_id, rule_data):
        """Helper method to update a blocking rule."""
        data = {'blocking_rule': rule_data}
        req = self.new_update_request(
            f'floatingips/{floatingip_id}/blocking-rules',
            data, rule_id, self.fmt)
        req.environ['neutron.context'] = self.context
        return req.get_response(self.ext_api)
    
    def _get_blocking_rule(self, floatingip_id, rule_id):
        """Helper method to get a blocking rule."""
        req = self.new_show_request(
            f'floatingips/{floatingip_id}/blocking-rules',
            rule_id, self.fmt)
        req.environ['neutron.context'] = self.context
        return req.get_response(self.ext_api)
    
    def _list_blocking_rules(self, floatingip_id, query_string=''):
        """Helper method to list blocking rules."""
        req = self.new_list_request(
            f'floatingips/{floatingip_id}/blocking-rules',
            self.fmt, query_string)
        req.environ['neutron.context'] = self.context
        return req.get_response(self.ext_api)
    
    def _delete_blocking_rule(self, floatingip_id, rule_id):
        """Helper method to delete a blocking rule."""
        req = self.new_delete_request(
            f'floatingips/{floatingip_id}/blocking-rules',
            rule_id)
        req.environ['neutron.context'] = self.context
        return req.get_response(self.ext_api)
    
    def test_create_blocking_rule_success(self):
        """Test successful creation of blocking rule."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress',
            'description': 'Block HTTP traffic'
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPCreated.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rule = result['blocking_rule']
        
        self.assertEqual(self.floatingip_id, rule['floatingip_id'])
        self.assertEqual('tcp', rule['protocol'])
        self.assertEqual(80, rule['local_port'])
        self.assertEqual('ingress', rule['direction'])
        self.assertEqual('Block HTTP traffic', rule['description'])
        self.assertIsNotNone(rule['id'])
        self.assertIsNotNone(rule['created_at'])
        self.assertIsNotNone(rule['updated_at'])
    
    def test_create_blocking_rule_minimal(self):
        """Test creation with minimal required fields."""
        rule_data = {
            'protocol': 'tcp',
            'direction': 'ingress'
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPCreated.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rule = result['blocking_rule']
        
        self.assertEqual('tcp', rule['protocol'])
        self.assertEqual('ingress', rule['direction'])
        self.assertIsNone(rule['local_port'])
        self.assertIsNone(rule['remote_ip'])
        self.assertIsNone(rule['remote_port'])
    
    def test_create_blocking_rule_all_fields(self):
        """Test creation with all fields specified."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 443,
            'remote_ip': '*************',
            'remote_port': 8080,
            'direction': 'egress',
            'description': 'Block HTTPS to specific server'
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPCreated.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rule = result['blocking_rule']
        
        self.assertEqual('tcp', rule['protocol'])
        self.assertEqual(443, rule['local_port'])
        self.assertEqual('*************', rule['remote_ip'])
        self.assertEqual(8080, rule['remote_port'])
        self.assertEqual('egress', rule['direction'])
        self.assertEqual('Block HTTPS to specific server', rule['description'])
    
    def test_create_blocking_rule_invalid_protocol(self):
        """Test creation with invalid protocol."""
        rule_data = {
            'protocol': 'invalid',
            'direction': 'ingress'
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
    
    def test_create_blocking_rule_invalid_direction(self):
        """Test creation with invalid direction."""
        rule_data = {
            'protocol': 'tcp',
            'direction': 'invalid'
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
    
    def test_create_blocking_rule_invalid_port_range(self):
        """Test creation with invalid port range."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 70000,  # Invalid port
            'direction': 'ingress'
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
    
    def test_create_blocking_rule_icmp_with_port(self):
        """Test creation of ICMP rule with port (should fail)."""
        rule_data = {
            'protocol': 'icmp',
            'local_port': 80,  # Invalid for ICMP
            'direction': 'ingress'
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
    
    def test_create_blocking_rule_missing_direction(self):
        """Test creation without required direction field."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80
            # Missing direction
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
    
    def test_create_blocking_rule_empty_rule(self):
        """Test creation with no meaningful fields."""
        rule_data = {
            'direction': 'ingress'
            # No protocol, ports, or IPs
        }
        
        res = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
    
    def test_create_blocking_rule_nonexistent_floatingip(self):
        """Test creation for non-existent floating IP."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        res = self._create_blocking_rule(
            floatingip_id='nonexistent-fip',
            rule_data=rule_data)
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
    
    def test_create_blocking_rule_conflict(self):
        """Test creation of conflicting rules."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Create first rule
        res1 = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPCreated.code, res1.status_int)
        
        # Try to create identical rule (should conflict)
        res2 = self._create_blocking_rule(rule_data=rule_data)
        self.assertEqual(exc.HTTPConflict.code, res2.status_int)
    
    def test_get_blocking_rule_success(self):
        """Test successful retrieval of blocking rule."""
        # Create a rule first
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        create_res = self._create_blocking_rule(rule_data=rule_data)
        created_rule = self.deserialize(self.fmt, create_res)['blocking_rule']
        rule_id = created_rule['id']
        
        # Get the rule
        res = self._get_blocking_rule(self.floatingip_id, rule_id)
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rule = result['blocking_rule']
        
        self.assertEqual(rule_id, rule['id'])
        self.assertEqual('tcp', rule['protocol'])
        self.assertEqual(80, rule['local_port'])
        self.assertEqual('ingress', rule['direction'])
    
    def test_get_blocking_rule_not_found(self):
        """Test retrieval of non-existent blocking rule."""
        res = self._get_blocking_rule(self.floatingip_id, 'nonexistent-rule')
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
    
    def test_get_blocking_rule_nonexistent_floatingip(self):
        """Test retrieval from non-existent floating IP."""
        res = self._get_blocking_rule('nonexistent-fip', 'some-rule')
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
    
    def test_list_blocking_rules_success(self):
        """Test successful listing of blocking rules."""
        # Create multiple rules
        rule_data_1 = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        rule_data_2 = {
            'protocol': 'tcp',
            'local_port': 443,
            'direction': 'ingress'
        }
        
        self._create_blocking_rule(rule_data=rule_data_1)
        self._create_blocking_rule(rule_data=rule_data_2)
        
        # List rules
        res = self._list_blocking_rules(self.floatingip_id)
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rules = result['blocking_rules']
        
        self.assertEqual(2, len(rules))
        ports = [rule['local_port'] for rule in rules]
        self.assertIn(80, ports)
        self.assertIn(443, ports)
    
    def test_list_blocking_rules_empty(self):
        """Test listing when no rules exist."""
        res = self._list_blocking_rules(self.floatingip_id)
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rules = result['blocking_rules']
        
        self.assertEqual(0, len(rules))
    
    def test_list_blocking_rules_with_filters(self):
        """Test listing with filters."""
        # Create rules with different protocols
        rule_data_tcp = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        rule_data_udp = {
            'protocol': 'udp',
            'local_port': 53,
            'direction': 'ingress'
        }
        
        self._create_blocking_rule(rule_data=rule_data_tcp)
        self._create_blocking_rule(rule_data=rule_data_udp)
        
        # Filter by protocol
        res = self._list_blocking_rules(
            self.floatingip_id, 
            query_string='protocol=tcp')
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rules = result['blocking_rules']
        
        self.assertEqual(1, len(rules))
        self.assertEqual('tcp', rules[0]['protocol'])
    
    def test_update_blocking_rule_success(self):
        """Test successful update of blocking rule."""
        # Create a rule first
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress',
            'description': 'Original description'
        }
        
        create_res = self._create_blocking_rule(rule_data=rule_data)
        created_rule = self.deserialize(self.fmt, create_res)['blocking_rule']
        rule_id = created_rule['id']
        
        # Update the rule
        update_data = {
            'local_port': 8080,
            'description': 'Updated description'
        }
        
        res = self._update_blocking_rule(
            self.floatingip_id, rule_id, update_data)
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rule = result['blocking_rule']
        
        self.assertEqual(rule_id, rule['id'])
        self.assertEqual('tcp', rule['protocol'])  # Unchanged
        self.assertEqual(8080, rule['local_port'])  # Updated
        self.assertEqual('ingress', rule['direction'])  # Unchanged
        self.assertEqual('Updated description', rule['description'])  # Updated
    
    def test_update_blocking_rule_not_found(self):
        """Test update of non-existent blocking rule."""
        update_data = {'description': 'New description'}
        
        res = self._update_blocking_rule(
            self.floatingip_id, 'nonexistent-rule', update_data)
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
    
    def test_update_blocking_rule_invalid_data(self):
        """Test update with invalid data."""
        # Create a rule first
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        create_res = self._create_blocking_rule(rule_data=rule_data)
        created_rule = self.deserialize(self.fmt, create_res)['blocking_rule']
        rule_id = created_rule['id']
        
        # Try to update with invalid port
        update_data = {'local_port': 70000}  # Invalid port
        
        res = self._update_blocking_rule(
            self.floatingip_id, rule_id, update_data)
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
    
    def test_update_blocking_rule_floatingip_id(self):
        """Test update attempting to change floatingip_id (should fail)."""
        # Create a rule first
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        create_res = self._create_blocking_rule(rule_data=rule_data)
        created_rule = self.deserialize(self.fmt, create_res)['blocking_rule']
        rule_id = created_rule['id']
        
        # Try to update floatingip_id
        update_data = {'floatingip_id': 'different-fip'}
        
        res = self._update_blocking_rule(
            self.floatingip_id, rule_id, update_data)
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
    
    def test_delete_blocking_rule_success(self):
        """Test successful deletion of blocking rule."""
        # Create a rule first
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        create_res = self._create_blocking_rule(rule_data=rule_data)
        created_rule = self.deserialize(self.fmt, create_res)['blocking_rule']
        rule_id = created_rule['id']
        
        # Delete the rule
        res = self._delete_blocking_rule(self.floatingip_id, rule_id)
        self.assertEqual(exc.HTTPNoContent.code, res.status_int)
        
        # Verify rule is gone
        get_res = self._get_blocking_rule(self.floatingip_id, rule_id)
        self.assertEqual(exc.HTTPNotFound.code, get_res.status_int)
    
    def test_delete_blocking_rule_not_found(self):
        """Test deletion of non-existent blocking rule."""
        res = self._delete_blocking_rule(
            self.floatingip_id, 'nonexistent-rule')
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
    
    def test_delete_blocking_rule_nonexistent_floatingip(self):
        """Test deletion from non-existent floating IP."""
        res = self._delete_blocking_rule('nonexistent-fip', 'some-rule')
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)


class TestFloatingIPBlockingMultiTenant(FloatingIPBlockingExtensionTestCase):
    """Test multi-tenant isolation and permissions."""
    
    def setUp(self):
        super(TestFloatingIPBlockingMultiTenant, self).setUp()
        self.project1_id = 'project-1'
        self.project2_id = 'project-2'
        self.user1_context = context.Context('user-1', self.project1_id)
        self.user2_context = context.Context('user-2', self.project2_id)
        self.admin_context = context.Context('admin', self.project1_id, 
                                           is_admin=True)
        
        # Add floating IPs for different projects
        self.plugin.floatingips.update({
            'fip-project1': {'id': 'fip-project1', 'project_id': self.project1_id},
            'fip-project2': {'id': 'fip-project2', 'project_id': self.project2_id},
        })
    
    def _create_blocking_rule_with_context(self, context, floatingip_id, 
                                          rule_data=None):
        """Helper to create rule with specific context."""
        if rule_data is None:
            rule_data = {
                'protocol': 'tcp',
                'local_port': 80,
                'direction': 'ingress'
            }
        
        data = {'blocking_rule': rule_data}
        req = self.new_create_request(
            f'floatingips/{floatingip_id}/blocking-rules',
            data, self.fmt)
        req.environ['neutron.context'] = context
        return req.get_response(self.ext_api)
    
    def _get_blocking_rules_with_context(self, context, floatingip_id):
        """Helper to list rules with specific context."""
        req = self.new_list_request(
            f'floatingips/{floatingip_id}/blocking-rules', self.fmt)
        req.environ['neutron.context'] = context
        return req.get_response(self.ext_api)
    
    def test_tenant_isolation_create(self):
        """Test that users can only create rules for their own floating IPs."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # User1 can create rule for their floating IP
        res = self._create_blocking_rule_with_context(
            self.user1_context, 'fip-project1', rule_data)
        self.assertEqual(exc.HTTPCreated.code, res.status_int)
        
        # User1 cannot create rule for user2's floating IP
        res = self._create_blocking_rule_with_context(
            self.user1_context, 'fip-project2', rule_data)
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
        
        # User2 can create rule for their floating IP
        res = self._create_blocking_rule_with_context(
            self.user2_context, 'fip-project2', rule_data)
        self.assertEqual(exc.HTTPCreated.code, res.status_int)
        
        # User2 cannot create rule for user1's floating IP
        res = self._create_blocking_rule_with_context(
            self.user2_context, 'fip-project1', rule_data)
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
    
    def test_tenant_isolation_list(self):
        """Test that users can only list rules for their own floating IPs."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Create rules for both projects
        self._create_blocking_rule_with_context(
            self.user1_context, 'fip-project1', rule_data)
        self._create_blocking_rule_with_context(
            self.user2_context, 'fip-project2', rule_data)
        
        # User1 can list their rules
        res = self._get_blocking_rules_with_context(
            self.user1_context, 'fip-project1')
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        result = self.deserialize(self.fmt, res)
        self.assertEqual(1, len(result['blocking_rules']))
        
        # User1 cannot list user2's rules
        res = self._get_blocking_rules_with_context(
            self.user1_context, 'fip-project2')
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
        
        # User2 can list their rules
        res = self._get_blocking_rules_with_context(
            self.user2_context, 'fip-project2')
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        result = self.deserialize(self.fmt, res)
        self.assertEqual(1, len(result['blocking_rules']))
        
        # User2 cannot list user1's rules
        res = self._get_blocking_rules_with_context(
            self.user2_context, 'fip-project1')
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
    
    def test_admin_access_all_projects(self):
        """Test that admin can access rules from all projects."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Create rules for both projects
        self._create_blocking_rule_with_context(
            self.user1_context, 'fip-project1', rule_data)
        self._create_blocking_rule_with_context(
            self.user2_context, 'fip-project2', rule_data)
        
        # Admin can access project1's rules
        res = self._get_blocking_rules_with_context(
            self.admin_context, 'fip-project1')
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        result = self.deserialize(self.fmt, res)
        self.assertEqual(1, len(result['blocking_rules']))
        
        # Admin can access project2's rules
        res = self._get_blocking_rules_with_context(
            self.admin_context, 'fip-project2')
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        result = self.deserialize(self.fmt, res)
        self.assertEqual(1, len(result['blocking_rules']))
    
    def test_project_id_in_response(self):
        """Test that project_id is correctly set in responses."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Create rule as user1
        res = self._create_blocking_rule_with_context(
            self.user1_context, 'fip-project1', rule_data)
        self.assertEqual(exc.HTTPCreated.code, res.status_int)
        
        result = self.deserialize(self.fmt, res)
        rule = result['blocking_rule']
        
        # Verify project_id matches the context
        self.assertEqual(self.project1_id, rule['project_id'])
    
    def test_cross_tenant_rule_access_denied(self):
        """Test that users cannot access rules from other tenants."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Create rule as user1
        create_res = self._create_blocking_rule_with_context(
            self.user1_context, 'fip-project1', rule_data)
        created_rule = self.deserialize(self.fmt, create_res)['blocking_rule']
        rule_id = created_rule['id']
        
        # User2 cannot get user1's rule
        req = self.new_show_request(
            f'floatingips/fip-project1/blocking-rules',
            rule_id, self.fmt)
        req.environ['neutron.context'] = self.user2_context
        res = req.get_response(self.ext_api)
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
        
        # User2 cannot update user1's rule
        update_data = {'description': 'Hacked'}
        req = self.new_update_request(
            f'floatingips/fip-project1/blocking-rules',
            {'blocking_rule': update_data}, rule_id, self.fmt)
        req.environ['neutron.context'] = self.user2_context
        res = req.get_response(self.ext_api)
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)
        
        # User2 cannot delete user1's rule
        req = self.new_delete_request(
            f'floatingips/fip-project1/blocking-rules', rule_id)
        req.environ['neutron.context'] = self.user2_context
        res = req.get_response(self.ext_api)
        self.assertEqual(exc.HTTPNotFound.code, res.status_int)


class TestFloatingIPBlockingValidation(FloatingIPBlockingExtensionTestCase):
    """Test validation functions and error handling."""
    
    def test_validate_blocking_rule_valid_tcp(self):
        """Test validation of valid TCP rule."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Should not raise exception
        floating_ip_blocking.validate_blocking_rule(rule_data)
    
    def test_validate_blocking_rule_valid_udp(self):
        """Test validation of valid UDP rule."""
        rule_data = {
            'protocol': 'udp',
            'remote_port': 53,
            'direction': 'egress'
        }
        
        # Should not raise exception
        floating_ip_blocking.validate_blocking_rule(rule_data)
    
    def test_validate_blocking_rule_valid_icmp(self):
        """Test validation of valid ICMP rule."""
        rule_data = {
            'protocol': 'icmp',
            'remote_ip': '***********',
            'direction': 'ingress'
        }
        
        # Should not raise exception
        floating_ip_blocking.validate_blocking_rule(rule_data)
    
    def test_validate_blocking_rule_valid_any_protocol(self):
        """Test validation of rule with 'any' protocol."""
        rule_data = {
            'protocol': 'any',
            'remote_ip': '10.0.0.0/24',
            'direction': 'ingress'
        }
        
        # Should not raise exception
        floating_ip_blocking.validate_blocking_rule(rule_data)
    
    def test_validate_blocking_rule_no_meaningful_fields(self):
        """Test validation fails when no meaningful fields are specified."""
        rule_data = {
            'direction': 'ingress'
            # No protocol, ports, or IPs
        }
        
        with self.assertRaises(floating_ip_blocking.InvalidBlockingRule):
            floating_ip_blocking.validate_blocking_rule(rule_data)
    
    def test_validate_blocking_rule_icmp_with_port(self):
        """Test validation fails for ICMP with port."""
        rule_data = {
            'protocol': 'icmp',
            'local_port': 80,  # Invalid for ICMP
            'direction': 'ingress'
        }
        
        with self.assertRaises(floating_ip_blocking.InvalidBlockingRule):
            floating_ip_blocking.validate_blocking_rule(rule_data)
    
    def test_validate_blocking_rule_missing_direction(self):
        """Test validation fails when direction is missing."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80
            # Missing direction
        }
        
        with self.assertRaises(floating_ip_blocking.InvalidBlockingRule):
            floating_ip_blocking.validate_blocking_rule(rule_data)
    
    def test_validate_rule_update_valid(self):
        """Test validation of valid rule update."""
        original_rule = {
            'id': 'rule-1',
            'floatingip_id': 'fip-1',
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        update_data = {
            'local_port': 8080,
            'description': 'Updated rule'
        }
        
        # Should not raise exception
        floating_ip_blocking.validate_rule_update(original_rule, update_data)
    
    def test_validate_rule_update_change_floatingip_id(self):
        """Test validation fails when trying to change floatingip_id."""
        original_rule = {
            'id': 'rule-1',
            'floatingip_id': 'fip-1',
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        update_data = {
            'floatingip_id': 'fip-2'  # Cannot change this
        }
        
        with self.assertRaises(floating_ip_blocking.InvalidBlockingRule):
            floating_ip_blocking.validate_rule_update(original_rule, update_data)
    
    def test_validate_rule_update_invalid_merged_rule(self):
        """Test validation fails when merged rule becomes invalid."""
        original_rule = {
            'id': 'rule-1',
            'floatingip_id': 'fip-1',
            'protocol': 'icmp',
            'direction': 'ingress'
        }
        
        update_data = {
            'local_port': 80  # Invalid for ICMP
        }
        
        with self.assertRaises(floating_ip_blocking.InvalidBlockingRule):
            floating_ip_blocking.validate_rule_update(original_rule, update_data)


class TestFloatingIPBlockingResponseFormat(FloatingIPBlockingExtensionTestCase):
    """Test API response format correctness."""
    
    def setUp(self):
        super(TestFloatingIPBlockingResponseFormat, self).setUp()
        self.floatingip_id = 'fip-1'
        self.project_id = 'project-1'
        self.context = context.Context('user-1', self.project_id)
    
    def test_create_response_format(self):
        """Test create response contains all required fields."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 443,
            'remote_ip': '*************',
            'remote_port': 8080,
            'direction': 'egress',
            'description': 'Test rule'
        }
        
        data = {'blocking_rule': rule_data}
        req = self.new_create_request(
            f'floatingips/{self.floatingip_id}/blocking-rules',
            data, self.fmt)
        req.environ['neutron.context'] = self.context
        res = req.get_response(self.ext_api)
        
        self.assertEqual(exc.HTTPCreated.code, res.status_int)
        result = self.deserialize(self.fmt, res)
        
        # Verify response structure
        self.assertIn('blocking_rule', result)
        rule = result['blocking_rule']
        
        # Verify all expected fields are present
        expected_fields = [
            'id', 'floatingip_id', 'project_id', 'protocol', 'local_port',
            'remote_ip', 'remote_port', 'direction', 'description',
            'created_at', 'updated_at'
        ]
        
        for field in expected_fields:
            self.assertIn(field, rule)
        
        # Verify field values
        self.assertEqual(self.floatingip_id, rule['floatingip_id'])
        self.assertEqual(self.project_id, rule['project_id'])
        self.assertEqual('tcp', rule['protocol'])
        self.assertEqual(443, rule['local_port'])
        self.assertEqual('*************', rule['remote_ip'])
        self.assertEqual(8080, rule['remote_port'])
        self.assertEqual('egress', rule['direction'])
        self.assertEqual('Test rule', rule['description'])
        self.assertIsNotNone(rule['id'])
        self.assertIsNotNone(rule['created_at'])
        self.assertIsNotNone(rule['updated_at'])
    
    def test_list_response_format(self):
        """Test list response format."""
        # Create multiple rules
        for port in [80, 443, 8080]:
            rule_data = {
                'protocol': 'tcp',
                'local_port': port,
                'direction': 'ingress'
            }
            data = {'blocking_rule': rule_data}
            req = self.new_create_request(
                f'floatingips/{self.floatingip_id}/blocking-rules',
                data, self.fmt)
            req.environ['neutron.context'] = self.context
            req.get_response(self.ext_api)
        
        # List rules
        req = self.new_list_request(
            f'floatingips/{self.floatingip_id}/blocking-rules', self.fmt)
        req.environ['neutron.context'] = self.context
        res = req.get_response(self.ext_api)
        
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        result = self.deserialize(self.fmt, res)
        
        # Verify response structure
        self.assertIn('blocking_rules', result)
        rules = result['blocking_rules']
        
        self.assertEqual(3, len(rules))
        
        # Verify each rule has required fields
        for rule in rules:
            self.assertIn('id', rule)
            self.assertIn('floatingip_id', rule)
            self.assertIn('protocol', rule)
            self.assertIn('direction', rule)
    
    def test_get_response_format(self):
        """Test get single rule response format."""
        # Create a rule
        rule_data = {
            'protocol': 'udp',
            'remote_port': 53,
            'direction': 'egress',
            'description': 'DNS query'
        }
        
        data = {'blocking_rule': rule_data}
        req = self.new_create_request(
            f'floatingips/{self.floatingip_id}/blocking-rules',
            data, self.fmt)
        req.environ['neutron.context'] = self.context
        create_res = req.get_response(self.ext_api)
        
        created_rule = self.deserialize(self.fmt, create_res)['blocking_rule']
        rule_id = created_rule['id']
        
        # Get the rule
        req = self.new_show_request(
            f'floatingips/{self.floatingip_id}/blocking-rules',
            rule_id, self.fmt)
        req.environ['neutron.context'] = self.context
        res = req.get_response(self.ext_api)
        
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        result = self.deserialize(self.fmt, res)
        
        # Verify response structure
        self.assertIn('blocking_rule', result)
        rule = result['blocking_rule']
        
        # Verify field values match created rule
        self.assertEqual(rule_id, rule['id'])
        self.assertEqual('udp', rule['protocol'])
        self.assertEqual(53, rule['remote_port'])
        self.assertEqual('egress', rule['direction'])
        self.assertEqual('DNS query', rule['description'])
        self.assertIsNone(rule['local_port'])  # Not specified
        self.assertIsNone(rule['remote_ip'])   # Not specified
    
    def test_update_response_format(self):
        """Test update response format."""
        # Create a rule
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress',
            'description': 'Original'
        }
        
        data = {'blocking_rule': rule_data}
        req = self.new_create_request(
            f'floatingips/{self.floatingip_id}/blocking-rules',
            data, self.fmt)
        req.environ['neutron.context'] = self.context
        create_res = req.get_response(self.ext_api)
        
        created_rule = self.deserialize(self.fmt, create_res)['blocking_rule']
        rule_id = created_rule['id']
        original_created_at = created_rule['created_at']
        
        # Update the rule
        update_data = {
            'local_port': 8080,
            'description': 'Updated'
        }
        
        data = {'blocking_rule': update_data}
        req = self.new_update_request(
            f'floatingips/{self.floatingip_id}/blocking-rules',
            data, rule_id, self.fmt)
        req.environ['neutron.context'] = self.context
        res = req.get_response(self.ext_api)
        
        self.assertEqual(exc.HTTPOk.code, res.status_int)
        result = self.deserialize(self.fmt, res)
        
        # Verify response structure
        self.assertIn('blocking_rule', result)
        rule = result['blocking_rule']
        
        # Verify updated fields
        self.assertEqual(rule_id, rule['id'])
        self.assertEqual(8080, rule['local_port'])  # Updated
        self.assertEqual('Updated', rule['description'])  # Updated
        self.assertEqual('tcp', rule['protocol'])  # Unchanged
        self.assertEqual('ingress', rule['direction'])  # Unchanged
        
        # Verify timestamps
        self.assertEqual(original_created_at, rule['created_at'])  # Unchanged
        self.assertNotEqual(original_created_at, rule['updated_at'])  # Changed
    
    def test_error_response_format(self):
        """Test error response format."""
        # Try to create invalid rule
        rule_data = {
            'protocol': 'invalid',
            'direction': 'ingress'
        }
        
        data = {'blocking_rule': rule_data}
        req = self.new_create_request(
            f'floatingips/{self.floatingip_id}/blocking-rules',
            data, self.fmt)
        req.environ['neutron.context'] = self.context
        res = req.get_response(self.ext_api)
        
        self.assertEqual(exc.HTTPBadRequest.code, res.status_int)
        
        # Verify error response contains meaningful message
        result = self.deserialize(self.fmt, res)
        self.assertIn('NeutronError', result)
        error = result['NeutronError']
        self.assertIn('message', error)
        self.assertIn('type', error)


class TestFloatingIPBlockingAuditLogging(FloatingIPBlockingExtensionTestCase):
    """Test audit logging functionality."""
    
    def setUp(self):
        super(TestFloatingIPBlockingAuditLogging, self).setUp()
        self.floatingip_id = 'fip-1'
        self.project_id = 'project-1'
        self.context = context.Context('user-1', self.project_id)
    
    @mock.patch('neutron.extensions.floating_ip_blocking.LOG')
    def test_log_rule_create_success(self, mock_log):
        """Test logging of successful rule creation."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'direction': 'ingress'
        }
        
        # Call log function directly
        floating_ip_blocking.log_rule_operation(
            self.context, 'create', self.floatingip_id, 
            rule_data=rule_data)
        
        # Verify log was called
        mock_log.info.assert_called_once()
        call_args = mock_log.info.call_args[0]
        log_message = call_args[0]
        log_data = call_args[1]['log_data']
        
        self.assertIn('operation', log_data)
        self.assertEqual('create', log_data['operation'])
        self.assertEqual('user-1', log_data['user_id'])
        self.assertEqual(self.project_id, log_data['project_id'])
        self.assertEqual(self.floatingip_id, log_data['floatingip_id'])
        self.assertIn('rule_data', log_data)
    
    @mock.patch('neutron.extensions.floating_ip_blocking.LOG')
    def test_log_rule_operation_error(self, mock_log):
        """Test logging of failed rule operation."""
        error_msg = "Validation failed"
        
        # Call log function with error
        floating_ip_blocking.log_rule_operation(
            self.context, 'create', self.floatingip_id, 
            error=error_msg)
        
        # Verify warning log was called
        mock_log.warning.assert_called_once()
        call_args = mock_log.warning.call_args[0]
        log_message = call_args[0]
        log_data = call_args[1]
        
        self.assertIn('error', log_data)
        self.assertEqual(error_msg, log_data['error'])
    
    @mock.patch('neutron.extensions.floating_ip_blocking.LOG')
    def test_log_rule_data_privacy(self, mock_log):
        """Test that sensitive data is not logged."""
        rule_data = {
            'protocol': 'tcp',
            'local_port': 80,
            'remote_ip': '*************',  # Should not be logged
            'direction': 'ingress'
        }
        
        # Call log function
        floating_ip_blocking.log_rule_operation(
            self.context, 'create', self.floatingip_id, 
            rule_data=rule_data)
        
        # Verify log was called
        mock_log.info.assert_called_once()
        call_args = mock_log.info.call_args[0]
        log_data = call_args[1]['log_data']
        
        # Verify safe data is logged
        safe_rule_data = log_data['rule_data']
        self.assertIn('protocol', safe_rule_data)
        self.assertIn('direction', safe_rule_data)
        self.assertIn('local_port', safe_rule_data)
        
        # Verify sensitive data is not logged
        self.assertNotIn('remote_ip', safe_rule_data)