[floating_ip_blocking]

#
# From neutron.services.floating_ip_blocking
#

# Enable or disable the floating IP blocking service (boolean value)
#enabled = true

# Maximum number of blocking rules per floating IP (integer value)
# Minimum value: 1
# Maximum value: 1000
#max_rules_per_fip = 100

# Enable automatic cleanup of empty blocking tables (boolean value)
#auto_cleanup_empty_tables = true

# Enable rule conflict detection (boolean value)
#enable_conflict_detection = true

# Default rule priority for new rules (integer value)
# Minimum value: 1
# Maximum value: 65535
#default_rule_priority = 1000

# Enable audit logging for rule operations (boolean value)
#enable_audit_logging = false

# Audit log file path (string value)
#audit_log_file = /var/log/neutron/floating_ip_blocking_audit.log

# Maximum number of rules to process in a single batch operation (integer value)
# Minimum value: 1
# Maximum value: 100
#batch_size = 10

# Timeout for rule synchronization to agents in seconds (integer value)
# Minimum value: 1
# Maximum value: 300
#sync_timeout = 30

# Enable performance monitoring and metrics collection (boolean value)
#enable_performance_monitoring = false

# Rule cache TTL in seconds (integer value)
# Minimum value: 60
# Maximum value: 3600
#rule_cache_ttl = 300

# Enable rule validation before applying to agents (boolean value)
#enable_rule_validation = true

# Maximum concurrent rule operations (integer value)
# Minimum value: 1
# Maximum value: 50
#max_concurrent_operations = 5