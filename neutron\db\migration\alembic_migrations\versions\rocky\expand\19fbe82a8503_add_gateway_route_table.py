# Copyright 2025 OpenStack Foundation
#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

"""add associate_type to routetables, and add routetablegatewaybindings

Revision ID: 19fbe82a8503
Revises: b1bca967e19d
Create Date: 2025-06-03 16:18:39.369683

"""

from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '19fbe82a8503'
down_revision = '682c319773d7'

routetables_associate_type = sa.Enum(
    'subnet', 'gateway',
    name='routetables_associate_type')


def upgrade():
    table_name = 'routetables'
    existColumn = False
    bind = op.get_bind()
    insp = sa.engine.reflection.Inspector.from_engine(bind)

    # Add associate_type to routetables
    for column in insp.get_columns(table_name):
        if column['name'] == 'associate_type':
            existColumn = True

    if not existColumn:
        op.add_column(table_name,
                      sa.Column('associate_type',
                                routetables_associate_type,
                                nullable=False))

    # Add routetablegatewaybindings
    exist_routetablegatewaybindings = False
    for table in insp.get_table_names():
        if table == 'routetablegatewaybindings':
            exist_routetablegatewaybindings = True

    if not exist_routetablegatewaybindings:
        op.create_table(
            'routetablegatewaybindings',
            sa.Column('routetable_id', sa.String(length=36), nullable=False),
            sa.Column('router_id', sa.String(length=36), nullable=False),
            sa.ForeignKeyConstraint(['router_id'], ['routers.id'],
                                    ondelete='CASCADE'),
            sa.ForeignKeyConstraint(['routetable_id'], ['routetables.id'],
                                    ondelete='CASCADE'),
            sa.PrimaryKeyConstraint('router_id', 'routetable_id')
        )
