#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.
#

from oslo_config import cfg

from neutron._i18n import _


FLOATING_IP_BLOCKING_OPTS = [
    cfg.BoolOpt('enable_auto_table_cleanup',
                default=True,
                help=_('Whether to automatically cleanup empty blocking '
                       'tables when all rules are deleted.')),
    cfg.BoolOpt('enable_rule_conflict_detection',
                default=True,
                help=_('Whether to enable rule conflict detection when '
                       'creating or updating blocking rules.')),
    cfg.IntOpt('max_blocking_rules_per_fip',
               default=100,
               help=_('Maximum number of blocking rules per floating IP. '
                      'Set to 0 to disable limit.')),
    cfg.BoolOpt('sync_rules_on_fip_status_change',
                default=True,
                help=_('Whether to synchronize blocking rule status when '
                       'floating IP status changes.')),
    cfg.ListOpt('allowed_protocols',
                default=['tcp', 'udp', 'icmp', 'any'],
                help=_('List of allowed protocols for blocking rules.')),
    cfg.BoolOpt('enable_detailed_conflict_analysis',
                default=True,
                help=_('Whether to provide detailed conflict analysis '
                       'when rule conflicts are detected.')),
    cfg.IntOpt('rule_validation_timeout',
               default=30,
               help=_('Timeout in seconds for rule validation operations.')),
    cfg.BoolOpt('enable_audit_logging',
                default=True,
                help=_('Whether to enable audit logging for blocking rule '
                       'operations.')),
    cfg.StrOpt('blocking_backend_driver',
               default='neutron.services.floating_ip_blocking.drivers.l3.'
                       'L3RpcDriver',
               help=_('Driver to be used for floating IP blocking plugin to '
                      'interact with L3 agents.')),
    cfg.BoolOpt('enable_lazy_table_creation',
                default=True,
                help=_('Whether to use lazy creation for blocking tables. '
                       'When enabled, tables are only created when the first '
                       'rule is added.')),
]


def register_floating_ip_blocking_opts(conf=cfg.CONF):
    """Register floating IP blocking configuration options."""
    conf.register_opts(FLOATING_IP_BLOCKING_OPTS, 'floating_ip_blocking')