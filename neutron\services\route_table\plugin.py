#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import functools

import netaddr
from neutron_lib.callbacks import events
from neutron_lib.callbacks import exceptions as callback_exc
from neutron_lib.callbacks import registry
from neutron_lib.callbacks import resources
from neutron_lib import constants as lib_consts
from neutron_lib.db import utils as db_utils
from neutron_lib import exceptions as n_exc
from neutron_lib.objects import exceptions as obj_exc
from neutron_lib.plugins import constants
from neutron_lib.plugins import directory
from neutron_lib.utils import helpers
from oslo_config import cfg
from oslo_log import log as logging

from neutron._i18n import _
from neutron.common import utils
from neutron.conf.services import route_table as route_table_conf
from neutron.db import _resource_extend as resource_extend
from neutron.db import api as db_api
from neutron.db import db_base_plugin_common
from neutron.db.models import route_table as models
from neutron.extensions import _route_table as apidef
from neutron.extensions import route_table as ext_rt
from neutron.objects import base as base_obj
from neutron.objects import route_table as route_table_obj
from neutron.services.route_table.common import exceptions as rt_exc
from neutron.services.route_table.drivers import l3 as l3rpc

LOG = logging.getLogger(__name__)
route_table_conf.register_route_table_opts()

# IP rule table id, start from 200
ROUTE_TABLE_ID = 200


def make_result_with_fields(f):
    @functools.wraps(f)
    def inner(*args, **kwargs):
        fields = kwargs.get('fields')
        result = f(*args, **kwargs)
        if fields is None:
            return result
        elif isinstance(result, list):
            return [db_utils.resource_fields(r, fields) for r in result]
        else:
            return db_utils.resource_fields(result, fields)

    return inner


@resource_extend.has_resource_extenders
@registry.has_registry_receivers
class RouteTablePlugin(ext_rt.RouteTablePluginBase):
    """Implementation of the Neutron Route Table Service Plugin.

    This class implements a Route Table plugin.
    """

    required_service_plugins = ['router']
    supported_extension_aliases = [apidef.ALIAS]

    __native_pagination_support = True
    __native_sorting_support = True
    __filter_validation_support = True

    def __init__(self):
        super(RouteTablePlugin, self).__init__()
        self.l3_plugin = directory.get_plugin(constants.L3)
        self._core_plugin = directory.get_plugin()
        self.driver = l3rpc.L3RpcDriver()

    def _get_route_table(self, context, id):
        obj = route_table_obj.RouteTable.get_object(context, id=id)
        if obj is None:
            raise rt_exc.RouteTableNotFound(id=id)
        return obj

    def _get_default_rt_id(self, context, router_id):
        """Fetch the default route table ID for the given router."""
        default_rt = route_table_obj.DefaultRouteTable.get_object(
            context, router_id=router_id)
        if default_rt:
            return default_rt.routetable_id

    def _ensure_default_route_table(self, context, router_id):
        """Ensure the default route table exists, if not create one."""
        default_rt_id = self._get_default_rt_id(context, router_id)
        if default_rt_id:
            return default_rt_id

        route_table = {
            'route_table':
                {'name': 'default',
                 'description': 'Default Route Table',
                 'router_id': router_id,
                 'project_id': context.project_id}
        }
        return self.create_route_table(context, route_table,
                                       default_rt=True)['id']

    def _ensure_gateway_route_table(self, context, router_id):
        """Ensure that a router can only have one gateway route table"""
        gateway_rt = \
            route_table_obj.RouteTable.get_obj_by_router_id_and_associate_type(
                context, router_id=router_id, associate_type='gateway')
        if gateway_rt:
            raise rt_exc.GatewayRouteTableExisted()

    def _ensure_default_route_table_subnet_bindings(self, context, router_id):
        # Get router information
        l3_plugin = directory.get_plugin(constants.L3)
        router = l3_plugin.get_router(context, router_id)
        filters = {'device_id': [router_id],
                   'device_owner': lib_consts.ROUTER_INTERFACE_OWNERS}
        interfaces = self._core_plugin.get_ports(context.elevated(), filters)

        for interface in interfaces:
            for fixed_ip in interface.get('fixed_ips', []):
                subnet = self._core_plugin.get_subnet(context.elevated(),
                                                      fixed_ip['subnet_id'])
                if not route_table_obj.RouteTableSubnetBindings.get_objects(
                        context, subnet_id=subnet.get('id')):
                    route_table = {
                        'route_table':
                            {'subnets': [subnet.get('id')]}
                    }
                    self.associate_subnets(context, router.get('id'),
                                           route_table)

    @staticmethod
    def _make_route_table_routes_list(routes):
        return [{'destination': str(route['destination']),
                 'nexthop': str(route['nexthop']),
                 'type': route['type']}
                for route in routes]

    def _generate_system_routes_for_router(self, context, router_id,
                                           ipv4_default_route_changed,
                                           ipv6_default_route_changed):
        """Generate system routes based on router configuration."""
        system_routes = []

        # Get router information
        l3_plugin = directory.get_plugin(constants.L3)
        router = l3_plugin.get_router(context, router_id)

        # Generate system_direct routes for internal ports
        filters = {'device_id': [router_id],
                   'device_owner': lib_consts.ROUTER_INTERFACE_OWNERS}
        interfaces = self._core_plugin.get_ports(context.elevated(), filters)

        for interface in interfaces:
            for fixed_ip in interface.get('fixed_ips', []):
                subnet = self._core_plugin.get_subnet(context.elevated(),
                                                      fixed_ip['subnet_id'])
                # Create direct route for subnet
                system_routes.append({
                    'destination': subnet['cidr'],
                    'nexthop': fixed_ip['ip_address'],
                    'type': 'system_direct'
                })
        # If the system default route has changed, don't generate it again
        if ipv4_default_route_changed and ipv6_default_route_changed:
            return system_routes

        # Generate default routes for external gateway
        if router.get('external_gateway_info'):
            gw_info = router['external_gateway_info']
            if gw_info.get('external_fixed_ips'):
                for fixed_ip in gw_info['external_fixed_ips']:
                    subnet = self._core_plugin.get_subnet(
                        context.elevated(), fixed_ip['subnet_id'])
                    if subnet.get('gateway_ip'):
                        # Create default route via gateway
                        ip_version = netaddr.IPAddress(
                            subnet['gateway_ip']).version
                        if ip_version == 4 and ipv4_default_route_changed:
                            continue
                        elif ip_version == 6 and ipv6_default_route_changed:
                            continue
                        default_dest = ('0.0.0.0/0' if ip_version == 4
                                        else '::/0')
                        system_routes.append({
                            'destination': default_dest,
                            'nexthop': subnet['gateway_ip'],
                            'type': 'default'
                        })
        return system_routes

    def _sync_system_routes_to_route_table(self, context, route_table_id,
                                           router_id):
        """Sync system routes to a specific route table."""
        if not route_table_id:
            return

        # Get current system routes from database
        current_routes = self._get_routes_by_route_table_id(context,
                                                            route_table_id)
        ipv4_default_route_changed = False
        ipv6_default_route_changed = False
        current_system_routes = []
        for route in current_routes:
            if (route['destination'] == '0.0.0.0/0' and
                    route['type'] != 'default'):
                ipv4_default_route_changed = True
            elif (route['destination'] == '::/0' and
                    route['type'] != 'default'):
                ipv6_default_route_changed = True
            elif route['type'] in ['default', 'system_direct']:
                current_system_routes.append(route)

        # Generate expected system routes
        expected_system_routes = self._generate_system_routes_for_router(
            context, router_id, ipv4_default_route_changed,
            ipv6_default_route_changed)

        try:
            routes_added, routes_removed = helpers.diff_list_of_dict(
                current_system_routes, expected_system_routes)
            with db_api.context_manager.writer.using(context):
                # Remove old system routes
                for route in routes_removed:
                    route_table_obj.RouteTableRoutes.delete_objects(
                        context,
                        routetable_id=route_table_id,
                        destination=route['destination'],
                        nexthop=route['nexthop'],
                        type=route['type']
                    )

                # Add new system routes
                for route in routes_added:
                    route_table_obj.RouteTableRoutes(
                        context,
                        type=route['type'],
                        routetable_id=route_table_id,
                        destination=utils.AuthenticIPNetwork(
                            route['destination']),
                        nexthop=netaddr.IPAddress(route['nexthop'])
                    ).create()

                # Update route table timestamp to trigger agent sync
                rt_obj = self._get_route_table(context, route_table_id)
                rt_obj.update()

        except Exception as e:
            LOG.error("Failed to sync system routes for route table %s: %s",
                     route_table_id, e)
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)

    def _sync_system_routes_to_default_table(self, context, router_id):
        """Sync system routes to default route table."""
        default_rt_id = self._get_default_rt_id(context, router_id)
        self._sync_system_routes_to_route_table(context, default_rt_id,
                                                router_id)

    def _sync_system_routes_to_all_route_tables(self, context, router_id):
        """Sync system routes to all route tables of a router."""
        # Get all route tables for this router
        route_tables = route_table_obj.RouteTable.get_objects(
            context, router_id=router_id)

        for rt in route_tables:
            # gateway route table does not need to generate system routes
            if rt.associate_type == 'gateway':
                continue
            self._sync_system_routes_to_route_table(context, rt.id, router_id)

    @staticmethod
    def _make_route_table_routes_info(route_table_id, tenant_id, routes):
        return {
            'route_table_id': route_table_id,
            'tenant_id': tenant_id,
            'routes': routes
        }

    @staticmethod
    def _make_route_table_bindings_list(bindings):
        return [bind['subnet_id'] for bind in bindings]

    @staticmethod
    def _make_route_table_gateway_binding_list(bindings):
        return [bind['router_id'] for bind in bindings]

    @staticmethod
    def _make_route_table_bindings_info(route_table_id, tenant_id, bindings):
        return {
            'route_table_id': route_table_id,
            'tenant_id': tenant_id,
            'subnets': bindings
        }

    @staticmethod
    def _make_route_table_gateway_binding_info(
            route_table_id, tenant_id, router_id):
        return {
            'route_table_id': route_table_id,
            'tenant_id': tenant_id,
            'router_id': router_id
        }

    def _make_route_table_dict(self, route_table, fields=None):
        res = {'id': route_table['id'],
               'name': route_table['name'],
               'router_id': route_table['router_id'],
               'tenant_id': route_table['tenant_id'],
               'description': route_table['description'],
               'table_id': route_table['table_id'],
               'associate_type': route_table['associate_type']}
        if route_table.routes:
            res['routes'] = self._make_route_table_routes_list(
                route_table.routes)
        else:
            res['routes'] = []
        if route_table.bound_subnets:
            res['bound_subnets'] = self._make_route_table_bindings_list(
                route_table.bound_subnets)
        else:
            res['bound_subnets'] = []
        res['default'] = route_table.is_default
        if route_table.bound_router:
            res['bound_router'] = self._make_route_table_gateway_binding_list(
                route_table.bound_router)
        else:
            res['bound_router'] = []
        resource_extend.apply_funcs(apidef.COLLECTION_NAME, res,
                                    route_table.db_obj)
        return db_utils.resource_fields(res, fields)

    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def _get_route_table_routes(self, context, filters=None, fields=None,
                                sorts=None, limit=None, marker=None,
                                page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        rt_route_obj = route_table_obj.RouteTableRoutes.get_objects(
            context, _pager=pager, **filters)
        return rt_route_obj

    def _get_routes_by_route_table_id(self, context, id):
        rt_route_obj = route_table_obj.RouteTableRoutes.get_objects(
            context, routetable_id=id)
        return self._make_route_table_routes_list(rt_route_obj)

    def _get_routes_by_router_id(self, context, router_id):
        qry_filter = context.session.query(models.RouteTable.id).filter(
            models.RouteTable.router_id == router_id)
        query = context.session.query(models.RouteTableRoutes)
        query = query.filter(
            models.RouteTableRoutes.routetable_id.in_(qry_filter))
        return self._make_route_table_routes_list(query)

    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def _get_associated_subnets(self, context, filters=None, fields=None,
                               sorts=None, limit=None, marker=None,
                               page_reverse=False):
        filters = filters or {}
        bindings = route_table_obj.RouteTableSubnetBindings.get_objects(
            context, **filters)
        return bindings

    @make_result_with_fields
    @db_base_plugin_common.convert_result_to_dict
    def _get_associated_gateway(self, context, filters=None, fields=None,
                               sorts=None, limit=None, marker=None,
                               page_reverse=False):
        filters = filters or {}
        binding = route_table_obj.RouteTableGatewayBindings.get_objects(
            context, **filters)
        return binding

    def _get_subnet_on_router(self, context, router_id):
        context = context.elevated()
        filters = {'device_id': [router_id]}
        ports = self._core_plugin.get_ports(context, filters)
        cidrs = []
        ips = []
        for port in ports:
            for ip in port['fixed_ips']:
                cidrs.append(self._core_plugin.get_subnet(
                    context, ip['subnet_id'])['cidr'])
                ips.append(ip['ip_address'])
        return cidrs, ips

    def _validate_routes_type(self, routes):
        for route in routes:
            nexthop_type = route['type']
            if nexthop_type not in cfg.CONF.supported_routes_nexthop_type:
                raise rt_exc.RoutesNexthopTypeNotSupport(nexthop=nexthop_type)

    def _validate_routes_nexthop(self, context, router_id, routes):
        cidrs, ips = self._get_subnet_on_router(context, router_id)
        for route in routes:
            nexthop = route['nexthop']
            if not netaddr.all_matching_cidrs(nexthop, cidrs):
                raise rt_exc.InvalidRoutes(
                    routes=routes,
                    reason='the nexthop is not connected with router')
            # Note(nati) nexthop should not be same as fixed_ips
            if nexthop in ips:
                raise rt_exc.InvalidRoutes(
                    routes=routes,
                    reason='the nexthop is used by router')

    def _validate_routes_destination(self, context, router_id, routes,
                                     associate_type):
        if associate_type == 'subnet':
            routes_dst, ips = self._get_subnet_on_router(context, router_id)
        else:
            routes_dst = []
        for route in routes:
            if route['destination'] in routes_dst:
                raise rt_exc.InvalidRoutes(
                    routes=routes,
                    reason='duplicate destination')
            routes_dst.append(route['destination'])

    def _validate_route_table_routes(self, context, id, router_id,
                                     associate_type, added_routes,
                                     all_routes):
        if len(all_routes) > cfg.CONF.max_route_table_routes:
            raise rt_exc.RouteTableRoutesExhausted(
                routetable_id=id,
                quota=cfg.CONF.max_route_table_routes)
        msg = ext_rt.validate_routetableroutes(added_routes)
        if msg:
            raise n_exc.BadRequest(resource=apidef.RESOURCE_NAME, msg=msg)
        # Validate route type
        self._validate_routes_type(added_routes)
        # Filter out system_direct routes for validation - they are managed
        # by system automatically.
        user_routes = [r for r in all_routes if r['type'] != 'system_direct']
        self._validate_routes_destination(context, router_id, user_routes,
                                          associate_type)
        self._validate_routes_nexthop(context, router_id, added_routes)

    def _validate_associate_subnets(self, context, router_id, subnets):
        filters = {'device_id': [router_id],
                   'device_owner': lib_consts.ROUTER_INTERFACE_OWNERS}
        interfaces = self._core_plugin.get_ports(
            context.elevated(), filters)
        router_subnets = []
        for inf in interfaces:
            for ip in inf['fixed_ips']:
                subnet = self._core_plugin.get_subnet(
                    context.elevated(),
                    id=ip['subnet_id'])
                router_subnets.append(subnet['id'])
        for subnet in subnets:
            if subnet not in router_subnets:
                raise rt_exc.FailedToAssociateSubnets(
                    error=(_("subnet %(subnet)s is not connect to router "
                             "%(router)s") % {'subnet': subnet,
                                              'router': router_id}))

    def _validate_associate_gateway(self, context, router_id):
        filters = {'device_id': [router_id],
                   'device_owner': [lib_consts.DEVICE_OWNER_ROUTER_GW]}
        interface = self._core_plugin.get_ports(context.elevated(), filters)
        if not interface:
            raise rt_exc.FailedToAssociateGateway(
                error=(_("router %(router_id)s has no gateway interface"))
            )

    def _assign_table_id(self, context, router_id):
        # assign table id according exist route tables id,
        # prevent id duplicate
        exist_route_tables = route_table_obj.RouteTable.get_objects(
            context, router_id=router_id)
        ids = [rt['table_id'] for rt in exist_route_tables]
        table_id = ROUTE_TABLE_ID
        for i in range(cfg.CONF.max_route_tables):
            table_id = ROUTE_TABLE_ID + i
            if table_id not in ids:
                return table_id
        return table_id

    @registry.receives(apidef.ROUTE_TABLE, [events.BEFORE_UPDATE])
    def _prevent_update_default_route_table(self, resource, event,
                                            trigger, payload=None):
        context = payload.context
        route_table_id = payload.resource_id
        rt = self._get_route_table(context, route_table_id)
        if rt.is_default:
            raise rt_exc.InvalidOperationForDefaultRouteTable(
                id=route_table_id,
                reason=_("default route table can't be updated"))

    @registry.receives(apidef.ROUTE_TABLE, [events.BEFORE_DELETE])
    def _prevent_delete_route_table(self, resource, event,
                                    trigger, payload=None):
        context = payload.context
        route_table_id = payload.resource_id
        delete_check = payload.request_body['delete_check']
        if not delete_check:
            return
        if route_table_obj.RouteTableSubnetBindings.get_objects(
                context, routetable_id=route_table_id):
            raise rt_exc.RouteTableInUse(id=route_table_id)

        if route_table_obj.RouteTableGatewayBindings.get_objects(
                context, routetable_id=route_table_id):
            raise rt_exc.GatewayRouteTableInUse(id=route_table_id)

        rt = self._get_route_table(context, route_table_id)
        if rt.is_default:
            raise rt_exc.InvalidOperationForDefaultRouteTable(
                id=route_table_id,
                reason=_("default route table can't be deleted by api"))

    @registry.receives(resources.ROUTER, [events.AFTER_CREATE])
    def _create_default_route_table(self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']
        # Default route table project_id equals with router project_id
        project_id = kwargs['router'].get('project_id')
        route_table = {
            'route_table':
                {'id': router_id,
                 'name': 'default',
                 'description': 'Default Route Table',
                 'router_id': router_id,
                 'project_id': project_id}
        }
        self.create_route_table(context, route_table, default_rt=True)
        # Sync system routes after creating default route table
        self._sync_system_routes_to_default_table(context, router_id)

    @registry.receives(resources.ROUTER_INTERFACE, [events.AFTER_CREATE])
    def _after_router_interface_create(self, resource, event, trigger,
                                       **kwargs):
        context = kwargs['context']
        # Associate the added subnet to router's default route table. The id
        # of default route table is the same as router_id.
        router_id = kwargs['router_id']
        subnet_id = kwargs['interface_info'].get('subnet_id')
        if subnet_id:
            with db_api.context_manager.writer.using(context):
                route_table_obj.RouteTableSubnetBindings(
                    context, subnet_id=subnet_id,
                    routetable_id=router_id).create()
        # Sync system routes to all route tables when router interface is added
        self._sync_system_routes_to_all_route_tables(context, router_id)

    @registry.receives(resources.ROUTER_INTERFACE, [events.AFTER_DELETE])
    def _after_router_interface_delete(self, resource, event, trigger,
                                       **kwargs):
        context = kwargs['context']
        # Disassociate the removed subnet from route table, which can not
        # be default route table.
        router_id = kwargs['router_id']
        subnet_id = kwargs['interface_info'].get('subnet_id')
        route_table_obj.RouteTableSubnetBindings.delete_objects(
            context, subnet_id=[subnet_id])
        # Sync system routes to all route tables when router interface
        # is removed
        self._sync_system_routes_to_all_route_tables(context, router_id)

    @registry.receives(resources.ROUTER_GATEWAY, [events.AFTER_CREATE])
    def _after_router_gateway_create(self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']
        # Sync system routes to all route tables when external gateway
        # is added
        self._sync_system_routes_to_all_route_tables(context, router_id)

    @registry.receives(resources.ROUTER_GATEWAY, [events.AFTER_UPDATE])
    def _after_router_gateway_update(self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']
        # Sync system routes to all route tables when external gateway
        # is updated
        self._sync_system_routes_to_all_route_tables(context, router_id)

    @registry.receives(resources.ROUTER_GATEWAY, [events.AFTER_DELETE])
    def _after_router_gateway_delete(self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']
        # Sync system routes to all route tables when external gateway
        # is removed
        self._sync_system_routes_to_all_route_tables(context, router_id)

    @registry.receives(resources.ROUTER, [events.PRECOMMIT_DELETE])
    def _delete_default_route_table(self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']
        rt = route_table_obj.DefaultRouteTable.get_object(
            context, router_id=router_id)
        if rt:
            self.delete_route_table(
                context, rt.routetable_id, delete_check=False)

    @registry.receives(resources.ROUTER_INTERFACE, [events.BEFORE_DELETE])
    def _prevent_delete_router_interface_use_by_routes(
            self, resource, event, trigger, **kwargs):
        context = kwargs['context']
        router_id = kwargs['router_id']
        subnet_id = kwargs['subnet_id']
        subnet = self._core_plugin.get_subnet(context, subnet_id)
        subnet_cidr = netaddr.IPNetwork(subnet['cidr'])
        rt_routes = self._get_routes_by_router_id(context, router_id)
        for route in rt_routes:
            if route['type'] in ['default', 'system_direct']:
                continue
            if netaddr.all_matching_cidrs(route['nexthop'], [subnet_cidr]):
                raise rt_exc.RouterInterfaceInUseByRouteTableRoutes(
                    router_id=router_id, subnet_id=subnet_id)

    @registry.receives(resources.ROUTER_GATEWAY, [events.BEFORE_DELETE])
    def _prevent_delete_router_gateway_use_by_gateway_route_table(
            self, resource, event, trigger, **kwargs):
        payload = kwargs['payload']
        context = payload.context
        router_id = payload.resource_id
        filters = {"router_id": router_id}
        gateway_binding = self._get_associated_gateway(
            context, filters=filters)
        if gateway_binding:
            raise rt_exc.RouterGatewayInUseByGatewayRouteTable(
                router_id=router_id)

    def _publish_route_table_event(self, event, payload):
        try:
            registry.publish(
                apidef.ROUTE_TABLE, event, self, payload=payload)
        except callback_exc.CallbackFailure as e:
            # raise the underlying exception
            LOG.exception(e)
            raise e.errors[0].error

    def _publish_route_table_route_event(self, event, payload):
        try:
            registry.publish(
                apidef.ROUTE_TABLE_ROUTE, event, self, payload=payload)
        except callback_exc.CallbackFailure as e:
            # raise the underlying exception
            LOG.exception(e)
            raise e.errors[0].error

    def _publish_route_table_subnet_binding_event(self, event, payload):
        try:
            registry.publish(
                apidef.ROUTE_TABLE_SUBNET_BINDING, event, self,
                payload=payload)
        except callback_exc.CallbackFailure as e:
            # raise the underlying exception
            LOG.exception(e)
            raise e.errors[0].error

    def _publish_route_table_gateway_binding_event(self, event, payload):
        try:
            registry.publish(
                apidef.ROUTE_TABLE_GATEWAY_BINDING, event, self,
                payload=payload)
        except callback_exc.CallbackFailure as e:
            # raise the underlying exception
            LOG.exception(e)
            raise e.errors[0].error

    def create_route_table(self, context, route_table, default_rt=False):
        rt = route_table[apidef.RESOURCE_NAME]
        router_id = rt.get('router_id')
        rt_routes = rt.pop('routes', [])
        associate_type = rt.get('associate_type', None)

        exist_rts = self.get_route_tables(context,
                                          {'router_id': [router_id]})
        if len(exist_rts) >= cfg.CONF.max_route_tables:
            raise rt_exc.RouteTablesExhausted(quota=cfg.CONF.max_route_tables)

        if rt_routes:
            self._validate_route_table_routes(context, '', rt['router_id'],
                                              rt['associate_type'], rt_routes,
                                              rt_routes)

        if not default_rt:
            self._ensure_default_route_table(context, router_id)
        else:
            exist_def_rt_id = self._get_default_rt_id(context, router_id)
            if exist_def_rt_id is not None:
                return self.get_route_table(context, exist_def_rt_id)

        if associate_type == 'gateway':
            self._ensure_gateway_route_table(context, router_id)
        try:
            rt.pop('tenant_id', None)
            table_id = self._assign_table_id(context, router_id)
            rt.update({'table_id': table_id,
                       'is_default': default_rt})

            with db_api.context_manager.writer.using(context):
                rt_obj = route_table_obj.RouteTable(context, **rt)
                rt_obj.create()
                payload = events.DBEventPayload(context=context,
                                                request_body=rt,
                                                states=(rt_obj,),
                                                resource_id=rt_obj.id)
                self._publish_route_table_event(events.PRECOMMIT_CREATE,
                                                payload)

                for route in rt_routes:
                    rt_route_obj = route_table_obj.RouteTableRoutes(
                        context,
                        type=route['type'],
                        routetable_id=rt_obj['id'],
                        destination=utils.AuthenticIPNetwork(
                            route['destination']),
                        nexthop=netaddr.IPAddress(route['nexthop']))
                    rt_route_obj.create()
                rt['routes'] = rt_routes
                payload = events.DBEventPayload(context=context,
                                                request_body=rt,
                                                resource_id=rt_obj.id)
                self._publish_route_table_route_event(
                    events.PRECOMMIT_CREATE, payload)

        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(
                error=e)
        self.driver.create_route_table(context, rt_obj)

        # Sync system routes to the newly created route table
        # Don't sync for default route table here, it's handled in router
        # creation. Don't sync for gateway route table.
        if not default_rt and rt['associate_type'] != 'gateway':
            self._sync_system_routes_to_route_table(context, rt_obj.id,
                                                    router_id)

        rt_obj = route_table_obj.RouteTable.get_object(context, id=rt_obj.id)
        return self._make_route_table_dict(rt_obj)

    def get_route_table(self, context, id, fields=None):
        rt_obj = self._get_route_table(context, id)
        return self._make_route_table_dict(rt_obj)

    def get_route_tables(self, context, filters=None, fields=None, sorts=None,
                         limit=None, marker=None, page_reverse=False):
        filters = filters or {}
        pager = base_obj.Pager(sorts, limit, page_reverse, marker)
        rt_objs = route_table_obj.RouteTable.get_objects(
            context, _pager=pager, **filters)
        return [self._make_route_table_dict(rt_obj) for rt_obj in rt_objs]

    def delete_route_table(self, context, id, delete_check=True):
        rt_obj = self._get_route_table(context, id)
        data = {'delete_check': delete_check}
        payload = events.DBEventPayload(
            context, request_body=data, states=(rt_obj,),
            resource_id=rt_obj.id)
        self._publish_route_table_event(events.BEFORE_DELETE, payload)
        with db_api.context_manager.writer.using(context):
            rt_obj.delete()
            payload = events.DBEventPayload(
                context, request_body=data, states=(rt_obj,),
                resource_id=rt_obj.id)
            self._publish_route_table_event(events.PRECOMMIT_DELETE, payload)
        self.driver.delete_route_table(context, rt_obj)

    def update_route_table(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        rt_obj = self._get_route_table(context, id)
        rt.pop('routes', [])
        payload = events.DBEventPayload(
            context, request_body=rt, states=(rt_obj,), resource_id=rt_obj.id)
        self._publish_route_table_event(events.BEFORE_UPDATE, payload)

        try:
            with db_api.context_manager.writer.using(context):
                rt_obj.update_fields(rt)
                rt_obj.update()
                payload = events.DBEventPayload(
                    context=context, request_body=rt, states=(rt_obj,),
                    resource_id=rt_obj.id)
                self._publish_route_table_event(events.PRECOMMIT_UPDATE,
                                                payload)
        except obj_exc.NeutronDbObjectDuplicateEntry as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)

        self.driver.update_route_table(context, rt_obj)
        return self._make_route_table_dict(rt_obj)

    def _get_system_routes(self, context, router_id, filters=None):
        """Get system routes for a specific router."""
        default_rt_id = self._get_default_rt_id(context, router_id)
        if not default_rt_id:
            return []

        # Get all routes from default route table
        all_routes = self._get_routes_by_route_table_id(context, default_rt_id)

        # Filter system routes
        system_routes = [r for r in all_routes
                        if r['type'] in ['default', 'system_direct']]

        # Apply additional filters if provided
        if filters:
            if 'type' in filters:
                system_routes = [r for r in system_routes
                                 if r['type'] in filters['type']]

        return system_routes

    def sync_route_table(self, context, id):
        """Manually trigger system routes synchronization for a router."""
        self._ensure_default_route_table(context, id)
        self._ensure_default_route_table_subnet_bindings(context, id)
        self._sync_system_routes_to_default_table(context, id)
        return self.get_route_table(context, id)

    def associate_subnets(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        rt_obj = self._get_route_table(context, id)
        subnets = rt['subnets']
        self._validate_associate_subnets(context, rt_obj['router_id'], subnets)
        try:
            route_table_obj.RouteTableSubnetBindings.delete_objects(
                context, subnet_id=subnets)
            with db_api.context_manager.writer.using(context):
                for subnet in subnets:
                    rt_subnet_binding_obj = (
                        route_table_obj.RouteTableSubnetBindings(
                            context, subnet_id=subnet, routetable_id=id))
                    rt_subnet_binding_obj.create()
                payload = events.DBEventPayload(
                    context=context, request_body=rt, resource_id=id)
                self._publish_route_table_subnet_binding_event(
                    events.PRECOMMIT_CREATE, payload)
                rt_obj.update()
        except obj_exc.NeutronDbObjectDuplicateEntry:
            raise rt_exc.FailedToAssociateSubnets(
                error=_("subnets %s have already been associated") % subnets)
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)
        self.driver.update_route_table(context, rt_obj)

        bindings_list = self._make_route_table_bindings_list(
            rt_obj.bound_subnets)
        return self._make_route_table_bindings_info(
            id, rt_obj.project_id, bindings_list)

    def disassociate_subnets(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        rt_obj = self._get_route_table(context, id)
        if rt_obj.is_default:
            raise rt_exc.InvalidOperationForDefaultRouteTable(
                id=id,
                reason=_("default route table doesn't "
                         "support disassociate subnets"))
        subnets = rt['subnets']
        try:
            route_table_obj.RouteTableSubnetBindings.delete_objects(
                context, subnet_id=subnets)
            with db_api.context_manager.writer.using(context):
                for subnet in subnets:
                    route_table_obj.RouteTableSubnetBindings(
                        context, subnet_id=subnet,
                        routetable_id=rt_obj.router_id).create()
                payload = events.DBEventPayload(
                    context=context, request_body=rt, resource_id=id)
                self._publish_route_table_subnet_binding_event(
                    events.PRECOMMIT_DELETE, payload)
                rt_obj.update()
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)
        self.driver.update_route_table(context, rt_obj)

        bindings_list = self._make_route_table_bindings_list(
            rt_obj.bound_subnets)
        return self._make_route_table_bindings_info(
            id, rt_obj.project_id, bindings_list)

    def associate_gateway(self, context, id):
        rt_obj = self._get_route_table(context, id)
        if rt_obj.associate_type != 'gateway':
            raise rt_exc.FailedToAssociateGateway(
                id=id,
                error=_("route table with associate_type != gateway doesn't "
                        "support associate gateway"))
        router_id = rt_obj.get('router_id')
        self._validate_associate_gateway(context, router_id)
        with db_api.context_manager.writer.using(context):
            route_table_obj.RouteTableGatewayBindings(
                context, router_id=router_id,
                routetable_id=id).create()
            payload = events.DBEventPayload(
                context=context, resource_id=id)
            self._publish_route_table_gateway_binding_event(
                events.PRECOMMIT_CREATE, payload)
        self.driver.update_route_table(context, rt_obj)
        return self._make_route_table_gateway_binding_info(
            id, rt_obj.project_id, router_id)

    def disassociate_gateway(self, context, id):
        rt_obj = self._get_route_table(context, id)
        if rt_obj.associate_type != 'gateway':
            raise rt_exc.FailedToDisassociateGateway(
                id=id,
                error=_("route table with associate_type != gateway doesn't "
                        "support disassociate gateway"))
        router_id = rt_obj.get('router_id')
        with db_api.context_manager.writer.using(context):
            binding = route_table_obj.RouteTableGatewayBindings.get_object(
                context, router_id=router_id, routetable_id=id)
            if binding:
                binding.delete()
                payload = events.DBEventPayload(
                    context=context, resource_id=id)
                self._publish_route_table_gateway_binding_event(
                    events.PRECOMMIT_DELETE, payload)
        self.driver.update_route_table(context, rt_obj)
        return self._make_route_table_gateway_binding_info(
            id, rt_obj.project_id, router_id)

    def get_route_table_routes(self, context, id):
        return self._get_routes_by_route_table_id(context, id)

    def add_route_table_routes(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        rt_obj = self._get_route_table(context, id)
        routes = rt['routes']

        old_routes = self._get_routes_by_route_table_id(context, id)
        self._validate_route_table_routes(context, id, rt_obj['router_id'],
                                          rt_obj['associate_type'],
                                          routes, old_routes + routes)

        # Prevent users from adding system_direct routes manually
        # Allow default routes to be managed manually
        for route in routes:
            if route['type'] == 'system_direct':
                raise n_exc.BadRequest(
                    resource=apidef.RESOURCE_NAME,
                    msg=_("Cannot manually add system_direct routes. "
                          "System_direct routes are managed automatically."))

        try:
            with db_api.context_manager.writer.using(context):
                for route in routes:
                    rt_route_obj = route_table_obj.RouteTableRoutes(
                        context,
                        type=route['type'],
                        routetable_id=id,
                        destination=utils.AuthenticIPNetwork(
                            route['destination']),
                        nexthop=netaddr.IPAddress(
                            route['nexthop']))
                    rt_route_obj.create()
                payload = events.DBEventPayload(context=context,
                                                request_body=rt,
                                                resource_id=id)
                self._publish_route_table_route_event(
                    events.PRECOMMIT_CREATE, payload)
                rt_obj.update()
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)

        self.driver.update_route_table(context, rt_obj)
        routes_list = self._make_route_table_routes_list(rt_obj.routes)
        return self._make_route_table_routes_info(
            id, rt_obj.project_id, routes_list)

    def update_route_table_routes(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        routes = rt['routes']
        rt_obj = self._get_route_table(context, id)
        self._validate_route_table_routes(context, id, rt_obj['router_id'],
                                          rt_obj['associate_type'], routes,
                                          routes)

        try:
            with db_api.context_manager.writer.using(context):
                for route in routes:
                    rt_route_obj = route_table_obj.RouteTableRoutes.get_object(
                        context,
                        routetable_id=id,
                        destination=route['destination'])
                    if rt_route_obj is None:
                        raise rt_exc.RouteTableRoutesNotFound(route=route)
                    rt_route_obj.update_fields(route)
                    rt_route_obj.update()
                payload = events.DBEventPayload(context=context,
                                                request_body=rt,
                                                resource_id=id)
                self._publish_route_table_route_event(
                    events.PRECOMMIT_UPDATE, payload)
                rt_obj.update()
        except rt_exc.RouteTableRoutesNotFound:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(
                error='route table routes not found')
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)
        self.driver.update_route_table(context, rt_obj)
        routes_list = self._make_route_table_routes_list(rt_obj.routes)
        return self._make_route_table_routes_info(
            id, rt_obj.project_id, routes_list)

    def remove_route_table_routes(self, context, id, route_table):
        rt = route_table[apidef.RESOURCE_NAME]
        routes = rt['routes']
        rt_obj = self._get_route_table(context, id)
        # Validate route type
        self._validate_routes_type(routes)
        # Prevent users from removing system_direct routes manually
        # Allow default routes to be managed manually
        for route in routes:
            if route['type'] == 'system_direct':
                raise n_exc.BadRequest(
                    resource=apidef.RESOURCE_NAME,
                    msg=_("Cannot manually remove system_direct routes. "
                          "System_direct routes are managed automatically."))

        try:
            with db_api.context_manager.writer.using(context):
                for route in routes:
                    rt_route_obj = route_table_obj.RouteTableRoutes.get_object(
                        context,
                        routetable_id=id,
                        destination=route['destination'],
                        nexthop=route['nexthop'],
                        type=route['type'])
                    if rt_route_obj is None:
                        raise rt_exc.RouteTableRoutesNotFound(route=route)
                    rt_route_obj.delete()
                payload = events.DBEventPayload(context=context,
                                                request_body=rt,
                                                resource_id=id)
                self._publish_route_table_route_event(
                    events.PRECOMMIT_DELETE, payload)
                rt_obj.update()
        except Exception as e:
            raise rt_exc.FailedToCreateOrUpdateRouteTable(error=e)
        self.driver.update_route_table(context, rt_obj)
        routes_list = self._make_route_table_routes_list(rt_obj.routes)
        return self._make_route_table_routes_info(
            id, rt_obj.project_id, routes_list)

    def sync_route_table_info(self, context, routers):
        if not routers:
            return

        for router in routers:
            route_tables = route_table_obj.RouteTable.get_objects(
                context, router_id=router['id'])

            rt_id_and_table_id_pairs = {}
            subnet_rt_id_and_table_id_pairs = {}
            gateway_rt_id_and_table_id_pair = {}
            for rt in route_tables:
                rt_id_and_table_id_pairs[rt['id']] = rt['table_id']
                if rt['associate_type'] == 'subnet':
                    subnet_rt_id_and_table_id_pairs[rt['id']] = rt['table_id']
                else:
                    gateway_rt_id_and_table_id_pair[rt['id']] = rt['table_id']
            router['rt_id_and_table_id_pairs'] = rt_id_and_table_id_pairs
            default_rt_id = self._get_default_rt_id(context, router['id'])
            router['default_route_table_id'] = default_rt_id
            filters = {"routetable_id": [rt['id'] for rt in route_tables]}
            route_table_routes = self._get_route_table_routes(
                context, filters=filters)
            router['route_table_routes'] = route_table_routes

            subnets_bindings = self._get_associated_subnets(
                context, filters=filters)
            for binding in subnets_bindings:
                cidr = self._core_plugin.get_subnet(
                    context, id=binding['subnet_id'])['cidr']
                binding.update({'cidr': cidr})
            router['subnet_route_table_bindings'] = subnets_bindings
            filters = {"router_id": router['id']}
            gateway_binding = self._get_associated_gateway(
                context, filters=filters)
            if gateway_binding:
                router['gateway_route_table_binding'] = gateway_binding[0]
            else:
                router['gateway_route_table_binding'] = {}
