#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import sqlalchemy as sa
from sqlalchemy import orm

from neutron_lib.db import constants as db_const
from neutron_lib.db import model_base

from neutron.db import standard_attr
from neutron.extensions import _route_table as apidef


class RouteTable(standard_attr.HasStandardAttributes, model_base.BASEV2,
                 model_base.HasId, model_base.HasProject):

    __tablename__ = 'routetables'

    name = sa.Column(sa.String(db_const.NAME_FIELD_SIZE))
    router_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                          sa.ForeignKey('routers.id', ondelete='CASCADE'),
                          nullable=False)
    table_id = sa.Column(sa.Integer(), nullable=False)

    api_collections = [apidef.COLLECTION_NAME]

    associate_type = sa.Column(sa.Enum(
        'subnet', 'gateway', name='routetables_associate_type'),
        default='subnet', nullable=False)


class DefaultRouteTable(model_base.BASEV2):
    __tablename__ = 'default_routetable'

    routetable_id = sa.Column(sa.String(36),
                              sa.ForeignKey('routetables.id',
                                            ondelete='CASCADE'),
                              nullable=False)
    router_id = sa.Column(sa.String(db_const.UUID_FIELD_SIZE),
                          sa.ForeignKey('routers.id'),
                          primary_key=True,
                          nullable=False)
    routetable = orm.relationship(
        RouteTable, lazy='joined',
        backref=orm.backref('default_routetable', cascade='all,delete'),
        primaryjoin='RouteTable.id==DefaultRouteTable.routetable_id')


class RouteTableRoutes(model_base.BASEV2):

    __tablename__ = 'routetable_routes'

    destination = sa.Column(sa.String(64), nullable=False, primary_key=True)
    nexthop = sa.Column(sa.String(64), nullable=False)
    type = sa.Column(sa.String(30), nullable=False)
    routetable_id = sa.Column(sa.String(36),
                              sa.ForeignKey('routetables.id',
                                            ondelete='CASCADE'),
                              primary_key=True)
    routetable = orm.relationship(RouteTable, load_on_pending=True,
                                  backref=orm.backref('routes',
                                                      lazy='subquery',
                                                      cascade='delete'))


class RouteTableSubnetBindings(model_base.BASEV2):

    __tablename__ = 'routetablesubnetbindings'

    routetable_id = sa.Column(sa.String(36),
                              sa.ForeignKey('routetables.id',
                                            ondelete='CASCADE'),
                              primary_key=True)
    subnet_id = sa.Column(sa.String(36),
                          sa.ForeignKey("subnets.id", ondelete='CASCADE'),
                          primary_key=True)

    routetable = orm.relationship(RouteTable, load_on_pending=True,
                                  backref=orm.backref('bound_subnets',
                                                      lazy='subquery',
                                                      cascade='delete'))


class RouteTableGatewayBindings(model_base.BASEV2):

    __tablename__ = 'routetablegatewaybindings'

    routetable_id = sa.Column(sa.String(36),
                              sa.ForeignKey('routetables.id',
                                            ondelete='CASCADE'),
                              primary_key=True)
    router_id = sa.Column(sa.String(36),
                          sa.ForeignKey("routers.id", ondelete='CASCADE'),
                          primary_key=True)

    routetable = orm.relationship(RouteTable, load_on_pending=True,
                                  backref=orm.backref('bound_router',
                                                      lazy='subquery',
                                                      cascade='delete'))
