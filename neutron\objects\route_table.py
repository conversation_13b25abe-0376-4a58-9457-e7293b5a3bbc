#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr

from oslo_versionedobjects import fields as obj_fields

from neutron.common import utils
from neutron.db.models import route_table as rt
from neutron.objects import base
from neutron.objects import common_types


@base.NeutronObjectRegistry.register
class RouteTable(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.RouteTable

    fields = {
        'id': common_types.UUIDField(),
        'project_id': obj_fields.StringField(nullable=True),
        'name': obj_fields.StringField(nullable=True),
        'router_id': common_types.UUIDField(),
        'table_id': obj_fields.IntegerField(),
        'routes': obj_fields.ListOfObjectsField(
            'RouteTableRoutes', nullable=True),
        'bound_subnets': obj_fields.ListOfObjectsField(
            'RouteTableSubnetBindings', nullable=True),
        'is_default': obj_fields.BooleanField(default=False),
        'associate_type': common_types.RouteTableAssociateTypeEnumField(
            default='subnet', nullable=False),
        'bound_router': obj_fields.ListOfObjectsField(
            'RouteTableGatewayBindings', nullable=True),
    }

    fields_no_update = ['project_id', 'is_default', 'associate_type']
    synthetic_fields = ['routes', 'bound_subnets', 'is_default',
                        'bound_router']
    extra_filter_names = {'is_default'}

    def from_db_object(self, db_obj):
        super(RouteTable, self).from_db_object(db_obj)
        if self._load_synthetic_fields:
            setattr(self, 'is_default',
                    bool(db_obj.get('default_routetable')))
            self.obj_reset_changes(['is_default'])

    def create(self):
        is_default = self.is_default
        with self.db_context_writer(self.obj_context):
            super(RouteTable, self).create()
            if is_default:
                default_route = DefaultRouteTable(
                    self.obj_context,
                    router_id=self.router_id,
                    routetable_id=self.id)
                default_route.create()
                self.is_default = True
                self.obj_reset_changes(['is_default'])

    @classmethod
    def get_obj_by_router_id_and_associate_type(
        cls, context, router_id, associate_type):
        query = context.session.query(cls.db_model)
        query = query.filter(cls.db_model.router_id == router_id)
        query = query.filter(cls.db_model.associate_type == associate_type)
        return query.first()


@base.NeutronObjectRegistry.register
class DefaultRouteTable(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.DefaultRouteTable

    fields = {
        'router_id': common_types.UUIDField(),
        'routetable_id': common_types.UUIDField(),
    }

    fields_no_update = ['routetable_id']
    primary_keys = ['router_id']


@base.NeutronObjectRegistry.register
class RouteTableRoutes(base.NeutronDbObject):
    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.RouteTableRoutes

    fields = {
        'routetable_id': common_types.UUIDField(),
        'destination': common_types.IPNetworkField(),
        'nexthop': obj_fields.IPAddressField(),
        'type': obj_fields.StringField()
    }

    primary_keys = ['routetable_id', 'destination']
    foreign_keys = {'RouteTable': {'routetable_id': 'id'}}

    @classmethod
    def modify_fields_from_db(cls, db_obj):
        result = super(RouteTableRoutes, cls).modify_fields_from_db(db_obj)
        if 'destination' in result:
            result['destination'] = utils.AuthenticIPNetwork(
                result['destination'])
        if 'nexthop' in result:
            result['nexthop'] = netaddr.IPAddress(result['nexthop'])
        return result

    @classmethod
    def modify_fields_to_db(cls, fields):
        result = super(RouteTableRoutes, cls).modify_fields_to_db(fields)
        if 'destination' in result:
            result['destination'] = cls.filter_to_str(result['destination'])
        if 'nexthop' in result:
            result['nexthop'] = cls.filter_to_str(result['nexthop'])
        return result


@base.NeutronObjectRegistry.register
class RouteTableSubnetBindings(base.NeutronDbObject):

    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.RouteTableSubnetBindings

    fields = {
        'routetable_id': common_types.UUIDField(),
        'subnet_id': common_types.UUIDField(),
    }

    primary_keys = ['subnet_id', 'routetable_id']
    foreign_keys = {'RouteTable': {'routetable_id': 'id'}}


@base.NeutronObjectRegistry.register
class RouteTableGatewayBindings(base.NeutronDbObject):

    # Version 1.0: Initial version
    VERSION = '1.0'

    db_model = rt.RouteTableGatewayBindings

    fields = {
        'routetable_id': common_types.UUIDField(),
        'router_id': common_types.UUIDField(),
    }

    primary_keys = ['router_id', 'routetable_id']
    foreign_keys = {'RouteTable': {'routetable_id': 'id'}}
