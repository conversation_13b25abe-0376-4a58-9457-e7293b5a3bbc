#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import netaddr
from neutron_lib.agent import l3_extension
from neutron_lib import constants
from neutron_lib.utils import helpers
from oslo_log import log as logging

from neutron.agent.linux import ip_lib
from neutron.common import coordination
from neutron.common import utils as common_utils

LOG = logging.getLogger(__name__)
SUBNET_ROUTE_TABLE_RULE_PRIORITY = 100
GATEWAY_ROUTE_TABLE_RULE_PRIORITY = 50
PREROUTING_ACCEPT_CHAIN = 'pre-accept'
PREROUTING_NOTRACK_CHAIN = 'pre-notrack'


class RouteTableAgentExtension(l3_extension.L3AgentExtension):

    def __init__(self):
        super(RouteTableAgentExtension, self).__init__()
        self.router_rt_info = {}

    def initialize(self, connection, driver_type):
        """Initialize agent extension."""
        pass

    def consume_api(self, agent_api):
        self.agent_api = agent_api

    def _get_router_info(self, router_id):
        router_info = self.agent_api.get_router_info(router_id)
        if router_info:
            return router_info
        LOG.debug("Router %s is not managed by this agent. "
                  "It was possibly deleted concurrently.", router_id)

    def _update_routing_table(self, operation, route, namespace, table_id):
        cmd = ['ip', 'route', operation, 'to', route['destination'],
               'via', route['nexthop'], 'table', table_id]
        ip_wrapper = ip_lib.IPWrapper(namespace=namespace)
        try:
            ip_wrapper.netns.execute(cmd, log_fail_as_error=False)
        except RuntimeError as e:
            # Change the following errors to warnings:
            # 1. Network is unreachable: the nexthop is not directly connected
            # 2. No such process: when deleting a route that does not exist
            if any(s in e.message for s in
                   ['Network is unreachable', 'No such process',
                    'Nexthop has invalid gateway']):
                LOG.warning(e.message)
            else:
                LOG.error(e.message)

    def _get_table_id(self, router_id, rt_id_and_table_id_pairs, id):
        table_id = rt_id_and_table_id_pairs.get(id)
        # If the route table is deleted, table_id
        rt_info = self.router_rt_info.get(router_id)
        return table_id or rt_info['rt_id_and_table_id_pairs'].get(id)

    def setup_route_table_routes(self, ri, router_id, namespace,
                                 route_table_routes, cidr_dev_pairs):
        rt_id_and_table_id_pairs = ri.router.get(
            'rt_id_and_table_id_pairs', {})
        rt_info = self.router_rt_info.get(router_id, {})
        old_routes = rt_info.get('route_table_routes', [])
        new_routes = route_table_routes
        routes_added, routes_removed = helpers.diff_list_of_dict(
            old_routes, new_routes)
        new_cidr_dev_pairs = cidr_dev_pairs
        old_cidr_dev_pairs = rt_info.get('cidr_dev_pairs', {})

        direct_routes_adds = []
        direct_routes_removes = []
        user_routes_adds = []
        user_routes_removes = []

        for route in routes_added:
            if route['type'] == 'system_direct':
                direct_routes_adds.append(route)
            else:
                user_routes_adds.append(route)

        for route in routes_removed:
            if route['type'] == 'system_direct':
                direct_routes_removes.append(route)
            else:
                user_routes_removes.append(route)

        # Process added direct routes (system routes)
        for route in direct_routes_adds:
            LOG.debug("Added direct route is '%s' ", route)
            for del_route in direct_routes_removes:
                if route['destination'] == del_route['destination']:
                    direct_routes_removes.remove(del_route)
            table_id = self._get_table_id(router_id, rt_id_and_table_id_pairs,
                                          route['routetable_id'])
            dev_name = new_cidr_dev_pairs[route['destination']]
            device = ip_lib.IPDevice(dev_name, namespace=namespace)
            common_utils.wait_until_true(device.exists, timeout=1)
            device.route.add_route(route['destination'], proto='kernel',
                                scope='link', src=route['nexthop'],
                                table=table_id)

        # Process removed direct routes (system routes)
        for route in direct_routes_removes:
            LOG.debug("Removed direct route is '%s' ", route)
            table_id = self._get_table_id(router_id, rt_id_and_table_id_pairs,
                                          route['routetable_id'])
            dev_name = old_cidr_dev_pairs[route['destination']]
            device = ip_lib.IPDevice(dev_name, namespace=namespace)
            LOG.debug('Device.exists(): %s', device.exists())
            if device.exists():
                device.route.delete_route(route['destination'], proto='kernel',
                                          scope='link', src=route['nexthop'],
                                          table=table_id)

        # Process added user routes (non-system routes)
        for route in user_routes_adds:
            LOG.debug("Added route table route is '%s' ", route)
            for del_route in user_routes_removes:
                if route['destination'] == del_route['destination']:
                    user_routes_removes.remove(del_route)
            table_id = self._get_table_id(router_id, rt_id_and_table_id_pairs,
                                          route['routetable_id'])
            self._update_routing_table('replace', route, namespace, table_id)

        # Handle removed user routes (non-system routes)
        for route in user_routes_removes:
            LOG.debug("Removed route table route is '%s'", route)
            table_id = self._get_table_id(router_id, rt_id_and_table_id_pairs,
                                          route['routetable_id'])
            if route['type'] == 'default':
                ex_gw_port = ri.get_ex_gw_port()
                if ex_gw_port:
                    ext_dev_name = ri.get_external_device_name(
                        ex_gw_port['id'])
                    device = ip_lib.IPDevice(ext_dev_name, namespace=namespace)
                    device.route.delete_gateway(route['nexthop'],
                                                table=table_id)
                else:
                    # If ex_gw_port is None, it indicates that the external
                    # gateway has been removed from the router. Therefore,
                    # there is no need to manually delete the default route.
                    continue
            else:
                self._update_routing_table('delete', route, namespace,
                                           table_id)

    def _binding_route_table_rule(self, namespace, cidr, dev, rt_index,
                                  priority):
        # Check if the rule already exists. If it does,
        # do not add it again. This is to avoid duplicate rules.
        ip_version = netaddr.IPNetwork(cidr).version
        if ip_version == 4:
            ip = '0.0.0.0/0'
        else:
            ip = '::/0'
        rules = ip_lib.list_ip_rules(namespace, ip_version)
        for rule in rules:
            if not rule.get('iif'):
                continue
            if (rule['iif'] == dev and
                    rule['table'] == str(rt_index)):
                LOG.debug('ip rule exist: iif %s', dev)
                return
        ip_lib.add_ip_rule(namespace, ip, iif=dev,
                           table=rt_index, priority=priority)

    def _unbinding_route_table_rule(self, namespace, cidr, iif, rt_index,
                                    priority):
        ip_version = netaddr.IPNetwork(cidr).version
        if ip_version == 4:
            ip = '0.0.0.0/0'
        else:
            ip = '::/0'
        ip_lib.delete_ip_rule(namespace, ip, iif=iif, table=rt_index,
                              priority=priority)

    def _clean_expired_rules_for_subnets(self, namespace,
                                         new_subnet_and_gw_dev_pairs):
        current_rules = (ip_lib.list_ip_rules(namespace, 4) +
                         ip_lib.list_ip_rules(namespace, 6))
        for rule in current_rules:
            if ('qr-' in rule.get('iif', '') and rule.get('iif') not in
                    new_subnet_and_gw_dev_pairs.values()):
                self._unbinding_route_table_rule(
                    namespace, rule['from'], rule['iif'],
                    rule['table'], rule['priority'])

    def setup_subnet_route_table_bindings(self, ri,
                                          router_id,
                                          namespace,
                                          subnet_bindings,
                                          subnet_and_gw_dev_pairs):
        rt_id_and_table_id_pairs = ri.router.get(
            'rt_id_and_table_id_pairs', {})
        rt_info = self.router_rt_info.get(router_id, {})
        old_bindings = rt_info.get('subnet_bindings', [])
        new_bindings = subnet_bindings
        old_subnet_and_gw_dev_pairs = rt_info['subnet_and_gw_dev_pairs']
        new_subnet_and_gw_dev_pairs = subnet_and_gw_dev_pairs

        associates, disassociates = helpers.diff_list_of_dict(
            old_bindings, new_bindings)
        for associate in associates:
            LOG.debug('route table %(rt)s associate with subnet %(subnet)s',
                      {'rt': associate['routetable_id'],
                       'subnet': associate['subnet_id']})
            table_id = self._get_table_id(router_id,
                                          rt_id_and_table_id_pairs,
                                          associate['routetable_id'])
            gateway_dev = new_subnet_and_gw_dev_pairs[associate['subnet_id']]
            self._binding_route_table_rule(
                namespace, associate['cidr'], gateway_dev,
                table_id, SUBNET_ROUTE_TABLE_RULE_PRIORITY)
        for disassociate in disassociates:
            LOG.debug('route table %(rt)s disassociate with subnet %(subnet)s',
                      {'rt': disassociate['routetable_id'],
                       'subnet': disassociate['subnet_id']})
            table_id = self._get_table_id(router_id,
                                          rt_id_and_table_id_pairs,
                                          disassociate['routetable_id'])
            gateway_dev = old_subnet_and_gw_dev_pairs[
                disassociate['subnet_id']]
            self._unbinding_route_table_rule(
                namespace, disassociate['cidr'], gateway_dev,
                table_id, SUBNET_ROUTE_TABLE_RULE_PRIORITY)
        # Clean the expired ip rules
        self._clean_expired_rules_for_subnets(namespace,
                                              new_subnet_and_gw_dev_pairs)

    def _clean_expired_rules_for_gateway(self, namespace):
        current_rules = (ip_lib.list_ip_rules(namespace, 4) +
                         ip_lib.list_ip_rules(namespace, 6))
        for rule in current_rules:
            if 'qg-' in rule.get('iif', ''):
                self._unbinding_route_table_rule(
                    namespace, rule['from'], rule['iif'],
                    rule['table'], rule['priority'])

    def setup_gateway_route_table_binding(self, ri, router_id, namespace,
                                          gateway_binding):
        rt_id_and_table_id_pairs = ri.router.get(
            'rt_id_and_table_id_pairs', {})
        rt_info = self.router_rt_info.get(router_id, {})
        old_gateway_binding = rt_info.get('gateway_binding', {})
        new_gateway_binding = gateway_binding
        # If the router did not bind the gateway routing table before and now
        # intends to bind it
        if not old_gateway_binding and new_gateway_binding:
            ex_gw_device_name = ri.get_external_device_name(
                ri.ex_gw_port['id'])
            table_id = self._get_table_id(router_id,
                                          rt_id_and_table_id_pairs,
                                          new_gateway_binding['routetable_id'])
            self._binding_route_table_rule(
                namespace, '0.0.0.0/0', ex_gw_device_name, table_id,
                GATEWAY_ROUTE_TABLE_RULE_PRIORITY)
            self._binding_route_table_rule(
                namespace, '::/0', ex_gw_device_name, table_id,
                GATEWAY_ROUTE_TABLE_RULE_PRIORITY)
        # If the router had previously bound the gateway routing table and now
        # intends to unbind it
        if old_gateway_binding and not new_gateway_binding:
            ex_gw_device_name = ri.get_external_device_name(
                ri.ex_gw_port['id'])
            table_id = self._get_table_id(router_id,
                                          rt_id_and_table_id_pairs,
                                          old_gateway_binding['routetable_id'])
            self._unbinding_route_table_rule(
                namespace, '0.0.0.0/0', ex_gw_device_name, table_id,
                GATEWAY_ROUTE_TABLE_RULE_PRIORITY)
            self._unbinding_route_table_rule(
                namespace, '::/0', ex_gw_device_name, table_id,
                GATEWAY_ROUTE_TABLE_RULE_PRIORITY)
        # Clean the expired rules
        if not old_gateway_binding and not new_gateway_binding:
            self._clean_expired_rules_for_gateway(namespace)

    def _setup_rules_for_rt_with_custom_default_route(
            self, ri, rt_ids_with_custom_default_route, rt_id_and_devs_pairs,
            rt_id_and_routes_pairs, iptables_manager):
        # If the subnet route table is associated with one or more subnets,
        # and the default routes have been modified, specific iptables rules
        # must be configured in the raw table to skip connection tracking
        # (conntrack) for the north-south traffic.
        for id in rt_ids_with_custom_default_route:
            # One subnet route table may be bound to multiple subnets
            devs = rt_id_and_devs_pairs.get(id)
            if not devs:
                # This subnet route table is not bound to any subnets
                continue
            for dev in devs:
                # Allow track metadata traffic
                rule = ('-d ***************/32 -i %(dev)s -j ACCEPT') % {
                        'dev': dev}
                rule = (PREROUTING_ACCEPT_CHAIN, rule)
                iptables_manager.ipv4['raw'].add_rule(*rule)
                # Allow track subnet traffic
                rt_routes = rt_id_and_routes_pairs[id]
                for rt_route in rt_routes:
                    # Skip ipv6 route
                    ip_version = netaddr.IPNetwork(
                        rt_route['destination']).version
                    if ip_version == 6:
                        continue
                    if rt_route['type'] == 'system_direct':
                        rule = ('-d %(cidr)s -i %(dev)s -j ACCEPT') % {
                            'cidr': rt_route['destination'],
                            'dev': dev}
                        rule = (PREROUTING_ACCEPT_CHAIN, rule)
                        iptables_manager.ipv4['raw'].add_rule(*rule)
                # Allow track floatingip traffic
                floatingips = ri.get_floating_ips()
                for floatingip in floatingips:
                    # Skip the gatewayip and ecs_ipv6 type floatingip.
                    # Skip the floaitngip with ipv6 address.
                    if (floatingip['fip_type'] in ['gatewayip', 'ecs_ipv6'] or
                        netaddr.IPAddress(
                            floatingip['floating_ip_address']).version == 6):
                        continue
                    rule = ('-d %(cidr)s/32 -i %(dev)s -j ACCEPT') % {
                            'cidr': floatingip['floating_ip_address'],
                            'dev': dev}
                    rule = (PREROUTING_ACCEPT_CHAIN, rule)
                    iptables_manager.ipv4['raw'].add_rule(*rule)
                # Notrack other traffic whose iif is <dev>
                rule = ('-i %(dev)s -j NOTRACK') % {'dev': dev}
                rule = (PREROUTING_NOTRACK_CHAIN, rule)
                iptables_manager.ipv4['raw'].add_rule(*rule)
        iptables_manager.apply()

    def _setup_rules_for_rt_with_custom_routes(
            self, rt_ids_with_custom_routes, rt_id_and_devs_pairs,
            rt_id_and_routes_pairs, iptables_manager):
        # If the subnet route table is associated with one or more subnets,
        # and subnet route table has cunstom routes, specific iptables rules
        # must be configured in the raw table to skip connection tracking
        # (conntrack) for the north-south traffic.
        for id in rt_ids_with_custom_routes:
            # One subnet route table may be bound to multiple subnets
            devs = rt_id_and_devs_pairs.get(id)
            if not devs:
                # This subnet route table is not bound to any subnets
                continue
            for dev in devs:
                rt_routes = rt_id_and_routes_pairs[id]
                for rt_route in rt_routes:
                    # Skip ipv6 route
                    ip_version = netaddr.IPNetwork(
                        rt_route['destination']).version
                    if ip_version == 6:
                        continue
                    if rt_route['type'] in ['default', 'system_direct']:
                        continue
                    rule = ('-d %(cidr)s -i %(dev)s -j NOTRACK') % {
                        'cidr': rt_route['destination'],
                        'dev': dev}
                    rule = (PREROUTING_NOTRACK_CHAIN, rule)
                    iptables_manager.ipv4['raw'].add_rule(*rule)
        iptables_manager.apply()

    def _setup_rules_for_rt_with_gateway_binding(
            self, gateway_binding, rt_id_and_routes_pairs, subnet_cidrs,
            cidr_dev_pairs, iptables_manager):
        # If the VPC gateway is bound to a gateway route table and has routing
        # rules like the following:
        # "***********/24 via ********** dev qr-c1bfb6bc-95",
        # the corresponding iptables rules should be added as follows:
        # "iptables -t raw -A neutron-l3-agent-pre-notrack ! -s 200.0.0.0/24
        # -d ***********/24 -i qr-c1bfb6bc-95 -j NOTRACK"
        if gateway_binding:
            gw_rt_id = gateway_binding['routetable_id']
            gw_rt_routes = rt_id_and_routes_pairs.get(gw_rt_id, [])
            for gw_rt_route in gw_rt_routes:
                nexthop = gw_rt_route['nexthop']
                # Match the CIDR corresponding to Nexthop, and the matching
                # result will only have one CIDR, because there will be no
                # subnets with overlapping CIDRs under the same VPC
                matching_cidr = netaddr.all_matching_cidrs(nexthop,
                                                           subnet_cidrs)
                dev = cidr_dev_pairs[str(matching_cidr[0])]
                rule = ('! -s %(nexthop)s -d %(destination)s -i %(dev)s '
                        '-j NOTRACK') % {
                        'nexthop': str(matching_cidr[0]),
                        'destination': gw_rt_route['destination'],
                        'dev': dev}
                rule = (PREROUTING_NOTRACK_CHAIN, rule)
                iptables_manager.ipv4['raw'].add_rule(*rule)
            iptables_manager.apply()

    def setup_raw_iptables_rules(self, ri, subnet_and_gw_dev_pairs,
                                 subnet_cidrs, cidr_dev_pairs):
        route_table_routes = ri.router.get('route_table_routes', [])
        subnet_bindings = ri.router.get('subnet_route_table_bindings', [])
        gateway_binding = ri.router.get('gateway_route_table_binding', {})
        # Clear rules on chains
        iptables_manager = ri.iptables_manager
        iptables_manager.ipv4['raw'].empty_chain(PREROUTING_ACCEPT_CHAIN)
        iptables_manager.ipv4['raw'].empty_chain(PREROUTING_NOTRACK_CHAIN)

        # Prepare route_table_id and dev_name pairs
        rt_id_and_devs_pairs = {}
        for subnet_binding in subnet_bindings:
            rt_id = subnet_binding['routetable_id']
            subnet_id = subnet_binding['subnet_id']
            if not rt_id_and_devs_pairs.get(rt_id):
                rt_id_and_devs_pairs[rt_id] = [
                    subnet_and_gw_dev_pairs[subnet_id]]
            else:
                rt_id_and_devs_pairs[rt_id].append(
                    subnet_and_gw_dev_pairs[subnet_id])

        # Prepare route_table_id and route_table_routes pairs
        rt_id_and_routes_pairs = {}
        rt_ids_with_custom_default_route = []
        rt_ids_with_custom_routes = []
        for rt_route in route_table_routes:
            rt_id = rt_route['routetable_id']
            if rt_id not in rt_id_and_routes_pairs:
                rt_id_and_routes_pairs[rt_id] = [rt_route]
            else:
                rt_id_and_routes_pairs[rt_id].append(rt_route)
            if (rt_route['destination'] == '0.0.0.0/0' and
                    rt_route['type'] != 'default'):
                rt_ids_with_custom_default_route.append(rt_id)
            if (rt_route['type'] not in ['default', 'system_direct'] and
                    rt_id not in rt_ids_with_custom_default_route and
                    rt_id not in rt_ids_with_custom_routes):
                rt_ids_with_custom_routes.append(rt_id)

        self._setup_rules_for_rt_with_custom_default_route(
            ri, rt_ids_with_custom_default_route, rt_id_and_devs_pairs,
            rt_id_and_routes_pairs, iptables_manager)

        self._setup_rules_for_rt_with_custom_routes(
            rt_ids_with_custom_routes, rt_id_and_devs_pairs,
            rt_id_and_routes_pairs, iptables_manager)

        self._setup_rules_for_rt_with_gateway_binding(
            gateway_binding, rt_id_and_routes_pairs, subnet_cidrs,
            cidr_dev_pairs, iptables_manager)

    @coordination.synchronized('route-table-router-{router_id}')
    def process_route_table(self, context, router_id):
        ri = self._get_router_info(router_id)
        # Route table extension does not support DVR.
        agent_mode = ri.agent_conf.agent_mode
        is_distributed_router = ri.router.get('distributed')
        if (agent_mode == constants.L3_AGENT_MODE_DVR or
                is_distributed_router):
            return

        # If the ha_state of the router is backup, the routes in the route
        # table cannot be generated. Therefore, process_route_table is skipped.
        if ri.router['ha'] and ri.ha_state == 'backup':
            # Clear route table related info on the agent side.
            self.router_rt_info[router_id] = {}
            return

        # Default route table is a special case of subnet route tables
        default_route_table_id = ri.router.get('default_route_table_id', None)
        if not default_route_table_id:
            return
        LOG.info("Processing route table routes and binding rules for "
                 "router %s", router_id)

        namespace = self.get_router_namespace(ri)
        route_table_routes = ri.router.get('route_table_routes', [])
        subnet_bindings = ri.router.get('subnet_route_table_bindings', [])
        gateway_binding = ri.router.get('gateway_route_table_binding', {})
        rt_id_and_table_id_pairs = ri.router.get(
            'rt_id_and_table_id_pairs', {})

        subnet_cidrs = []
        cidr_dev_pairs = {}
        subnet_and_gw_dev_pairs = {}
        internal_ports = ri.internal_ports
        for internal_port in internal_ports:
            device_name = ri.get_internal_device_name(internal_port['id'])
            # The internal port qr-dev of the router is associated with only
            # one single subnet.
            subnet = internal_port['subnets'][0]
            subnet_cidrs.append(subnet['cidr'])
            cidr_dev_pairs[subnet['cidr']] = device_name
            subnet_and_gw_dev_pairs[subnet['id']] = device_name

        # Cache route table related info on the agent side.
        rt_info = self.router_rt_info.get(router_id, {})
        if not rt_info:
            rt_info['route_table_routes'] = []
            rt_info['subnet_bindings'] = []
            rt_info['gateway_binding'] = {}
            rt_info['rt_id_and_table_id_pairs'] = {}
            rt_info['subnet_and_gw_dev_pairs'] = {}
            rt_info['cidr_dev_pairs'] = {}
            self.router_rt_info.update({router_id: rt_info})

        # Set route table routes
        self.setup_route_table_routes(ri, router_id, namespace,
                                      route_table_routes, cidr_dev_pairs)
        # Bind or unbind subnet route tables to subnet gateways
        # and
        # unbind or bind default route table from subnet gateways
        self.setup_subnet_route_table_bindings(ri, router_id, namespace,
                                               subnet_bindings,
                                               subnet_and_gw_dev_pairs)

        # Bind or unbind gateway route table to external gateway
        self.setup_gateway_route_table_binding(ri, router_id, namespace,
                                               gateway_binding)

        # Setup iptables rules of raw table to skip conntrack for packets
        # at appropriate times
        self.setup_raw_iptables_rules(ri, subnet_and_gw_dev_pairs,
                                      subnet_cidrs, cidr_dev_pairs)

        # Update the route table related info.
        rt_info['route_table_routes'] = route_table_routes
        rt_info['subnet_bindings'] = subnet_bindings
        rt_info['gateway_binding'] = gateway_binding
        rt_info['rt_id_and_table_id_pairs'] = rt_id_and_table_id_pairs
        rt_info['subnet_and_gw_dev_pairs'] = subnet_and_gw_dev_pairs
        rt_info['cidr_dev_pairs'] = cidr_dev_pairs
        self.router_rt_info.update({router_id: rt_info})

    def get_router_namespace(self, router_info):
        # Legacy/HA router
        namespace = router_info.ns_name
        return namespace

    def add_router(self, context, data):
        self.process_route_table(context, data['id'])

    def update_router(self, context, data):
        self.process_route_table(context, data['id'])

    def delete_router(self, context, data):
        # Clear route table related info on the agent side
        self.router_rt_info[data['id']] = {}

    def ha_state_change(self, context, data):
        self.process_route_table(context, data['router_id'])
