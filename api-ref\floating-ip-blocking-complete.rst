==========================
Floating IP Blocking Rules
==========================

The Floating IP Blocking extension provides fine-grained traffic control for floating IPs through 5-tuple based blocking rules.

.. contents::
   :local:
   :depth: 2

API Overview
============

The Floating IP Blocking API allows you to create, read, update, and delete blocking rules for floating IPs. 
The API supports seven different blocking modes, from simple 1-tuple (floating IP only) to complete 5-tuple 
(protocol, local port, remote IP, remote port, direction) specifications.

Supported Blocking Modes
-------------------------

1. **1-tuple**: Block all traffic to/from floating IP
2. **2-tuple**: Block traffic from specific remote IP/subnet
3. **3-tuple A**: Block specific protocol + local port
4. **3-tuple B**: Block specific protocol + remote port  
5. **4-tuple A**: Block protocol + local port + remote IP
6. **4-tuple B**: Block protocol + remote IP + remote port
7. **5-tuple**: Complete specification with all fields

API Endpoints
=============

List Floating IP Blocking Rules
================================

.. rest_method:: GET /v2.0/floatingips/{floatingip_id}/blocking-rules

Lists blocking rules for a specific floating IP.

Normal response codes: 200

Error response codes: 400, 401, 403, 404

Request Parameters
------------------

.. rest_parameters:: 

   floatingip_id: floatingip_id-path
     description: The UUID of the floating IP.
     in: path
     required: true
     type: string

Response Parameters
-------------------

.. rest_parameters::

   blocking_rules: blocking_rules
     description: A list of blocking_rule objects.
     in: body
     required: true
     type: array

   id: blocking_rule_id
     description: The UUID of the blocking rule.
     in: body
     required: true
     type: string

   floatingip_id: floatingip_id
     description: The UUID of the associated floating IP.
     in: body
     required: true
     type: string

   protocol: blocking_rule_protocol
     description: The IP protocol. Values are tcp, udp, icmp, or any. null if not specified.
     in: body
     required: true
     type: string

   local_port: blocking_rule_local_port
     description: The local port number. null if not specified.
     in: body
     required: true
     type: integer

   remote_ip: blocking_rule_remote_ip
     description: The remote IP address or CIDR. null if not specified.
     in: body
     required: true
     type: string

   remote_port: blocking_rule_remote_port
     description: The remote port number. null if not specified.
     in: body
     required: true
     type: integer

   direction: blocking_rule_direction
     description: The traffic direction. Values are ingress or egress.
     in: body
     required: true
     type: string

   description: blocking_rule_description
     description: A human-readable description for the blocking rule.
     in: body
     required: true
     type: string

   created_at: created_at
     description: Time at which the resource was created.
     in: body
     required: true
     type: string

   updated_at: updated_at
     description: Time at which the resource was last updated.
     in: body
     required: true
     type: string

Response Example
----------------

.. code-block:: json

   {
       "blocking_rules": [
           {
               "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
               "floatingip_id": "2f245a7b-796b-4f26-9cf9-9e82d248fda7",
               "protocol": "tcp",
               "local_port": 80,
               "remote_ip": "*************",
               "remote_port": null,
               "direction": "ingress",
               "description": "Block HTTP access from specific IP",
               "created_at": "2025-01-19T10:00:00Z",
               "updated_at": "2025-01-19T10:00:00Z"
           }
       ]
   }

Create Floating IP Blocking Rule
=================================

.. rest_method:: POST /v2.0/floatingips/{floatingip_id}/blocking-rules

Creates a blocking rule for a specific floating IP.

Normal response codes: 201

Error response codes: 400, 401, 403, 404, 409

Request Parameters
------------------

.. rest_parameters::

   floatingip_id: floatingip_id-path
     description: The UUID of the floating IP.
     in: path
     required: true
     type: string

   blocking_rule: blocking_rule
     description: A blocking_rule object.
     in: body
     required: true
     type: object

   protocol: blocking_rule_protocol-request
     description: The IP protocol. Valid values are tcp, udp, icmp, or any.
     in: body
     required: false
     type: string

   local_port: blocking_rule_local_port-request
     description: The local port number. Valid range is 1-65535.
     in: body
     required: false
     type: integer

   remote_ip: blocking_rule_remote_ip-request
     description: The remote IP address or CIDR.
     in: body
     required: false
     type: string

   remote_port: blocking_rule_remote_port-request
     description: The remote port number. Valid range is 1-65535.
     in: body
     required: false
     type: integer

   direction: blocking_rule_direction-request
     description: The traffic direction. Valid values are ingress or egress.
     in: body
     required: true
     type: string

   description: blocking_rule_description-request
     description: A human-readable description for the blocking rule.
     in: body
     required: false
     type: string

Request Example
---------------

.. code-block:: json

   {
       "blocking_rule": {
           "protocol": "tcp",
           "local_port": 22,
           "remote_ip": "10.0.0.0/8",
           "direction": "ingress",
           "description": "Block SSH access from private networks"
       }
   }

Response Parameters
-------------------

Same as List Blocking Rules response parameters.

Response Example
----------------

.. code-block:: json

   {
       "blocking_rule": {
           "id": "f47ac10b-58cc-4372-a567-0e02b2c3d479",
           "floatingip_id": "2f245a7b-796b-4f26-9cf9-9e82d248fda7",
           "protocol": "tcp",
           "local_port": 22,
           "remote_ip": "10.0.0.0/8",
           "remote_port": null,
           "direction": "ingress",
           "description": "Block SSH access from private networks",
           "created_at": "2025-01-19T10:00:00Z",
           "updated_at": "2025-01-19T10:00:00Z"
       }
   }

Show Floating IP Blocking Rule
===============================

.. rest_method:: GET /v2.0/floatingips/{floatingip_id}/blocking-rules/{rule_id}

Shows details for a specific blocking rule.

Normal response codes: 200

Error response codes: 400, 401, 403, 404

Request Parameters
------------------

.. rest_parameters::

   floatingip_id: floatingip_id-path
   rule_id: blocking_rule_id-path
     description: The UUID of the blocking rule.
     in: path
     required: true
     type: string

Response Parameters
-------------------

Same as Create Blocking Rule response parameters.

Update Floating IP Blocking Rule
=================================

.. rest_method:: PUT /v2.0/floatingips/{floatingip_id}/blocking-rules/{rule_id}

Updates a specific blocking rule.

Normal response codes: 200

Error response codes: 400, 401, 403, 404, 409

Request Parameters
------------------

Same as Create Blocking Rule request parameters, plus:

.. rest_parameters::

   rule_id: blocking_rule_id-path

Request Example
---------------

.. code-block:: json

   {
       "blocking_rule": {
           "protocol": "tcp",
           "local_port": 8080,
           "remote_ip": "***********/24",
           "direction": "ingress",
           "description": "Block HTTP access from entire subnet"
       }
   }

Delete Floating IP Blocking Rule
=================================

.. rest_method:: DELETE /v2.0/floatingips/{floatingip_id}/blocking-rules/{rule_id}

Deletes a specific blocking rule.

Normal response codes: 204

Error response codes: 400, 401, 403, 404

Request Parameters
------------------

.. rest_parameters::

   floatingip_id: floatingip_id-path
   rule_id: blocking_rule_id-path

Response
--------

There is no body content for the response of a successful DELETE request.

Error Responses
===============

The Floating IP Blocking API returns standard HTTP status codes and detailed error messages.

HTTP Status Codes
------------------

Success Codes
~~~~~~~~~~~~~

* **200 OK** - The request was successful.
* **201 Created** - The resource was successfully created.
* **204 No Content** - The request was successful and there is no content to return.

Client Error Codes
~~~~~~~~~~~~~~~~~~

* **400 Bad Request** - The request was malformed or contains invalid parameters.
* **401 Unauthorized** - Authentication is required and has failed.
* **403 Forbidden** - The request is understood but access is denied.
* **404 Not Found** - The requested resource could not be found.
* **409 Conflict** - The request conflicts with the current state of the resource.

Server Error Codes
~~~~~~~~~~~~~~~~~~

* **500 Internal Server Error** - An unexpected error occurred on the server.
* **503 Service Unavailable** - The service is temporarily unavailable.

Error Response Format
---------------------

All error responses follow a consistent JSON format:

.. code-block:: json

   {
       "error": {
           "type": "ErrorType",
           "message": "Human-readable error message",
           "details": "Additional error details (optional)"
       }
   }

Specific Error Types
--------------------

FloatingIPNotFound (404)
~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
       "error": {
           "type": "FloatingIPNotFound",
           "message": "Floating IP 2f245a7b-796b-4f26-9cf9-9e82d248fda7 not found"
       }
   }

BlockingRuleNotFound (404)
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
       "error": {
           "type": "BlockingRuleNotFound",
           "message": "Blocking rule f47ac10b-58cc-4372-a567-0e02b2c3d479 not found"
       }
   }

InvalidBlockingRule (400)
~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
       "error": {
           "type": "InvalidBlockingRule",
           "message": "Invalid blocking rule: port number must be between 1 and 65535"
       }
   }

BlockingRuleConflict (409)
~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: json

   {
       "error": {
           "type": "BlockingRuleConflict",
           "message": "Blocking rule conflicts with existing rule 6ba7b810-9dad-11d1-80b4-00c04fd430c8",
           "details": {
               "conflict_type": "DUPLICATE",
               "existing_rule_id": "6ba7b810-9dad-11d1-80b4-00c04fd430c8",
               "suggestion": "Rule with identical parameters already exists"
           }
       }
   }

Seven Blocking Modes Examples
==============================

Mode 1: 1-tuple (Block All Traffic)
------------------------------------

Block all ingress traffic to the floating IP:

.. code-block:: json

   {
       "blocking_rule": {
           "direction": "ingress",
           "description": "Block all ingress traffic to this floating IP"
       }
   }

Mode 2: 2-tuple (Block from Specific IP/Subnet)
------------------------------------------------

Block all traffic from a specific subnet:

.. code-block:: json

   {
       "blocking_rule": {
           "remote_ip": "***********/24",
           "direction": "ingress",
           "description": "Block all traffic from specific subnet"
       }
   }

Mode 3A: 3-tuple (Protocol + Local Port)
-----------------------------------------

Block HTTP traffic to the floating IP:

.. code-block:: json

   {
       "blocking_rule": {
           "protocol": "tcp",
           "local_port": 80,
           "direction": "ingress",
           "description": "Block HTTP traffic to this floating IP"
       }
   }

Mode 3B: 3-tuple (Protocol + Remote Port)
------------------------------------------

Block outbound traffic to port 8080:

.. code-block:: json

   {
       "blocking_rule": {
           "protocol": "tcp",
           "remote_port": 8080,
           "direction": "egress",
           "description": "Block outbound traffic to port 8080"
       }
   }

Mode 4A: 4-tuple (Protocol + Local Port + Remote IP)
-----------------------------------------------------

Block HTTP from private networks:

.. code-block:: json

   {
       "blocking_rule": {
           "protocol": "tcp",
           "local_port": 80,
           "remote_ip": "10.0.0.0/8",
           "direction": "ingress",
           "description": "Block HTTP from private networks"
       }
   }

Mode 4B: 4-tuple (Protocol + Remote IP + Remote Port)
------------------------------------------------------

Block MySQL access to specific server:

.. code-block:: json

   {
       "blocking_rule": {
           "protocol": "tcp",
           "remote_ip": "*************",
           "remote_port": 3306,
           "direction": "egress",
           "description": "Block MySQL access to specific server"
       }
   }

Mode 5: 5-tuple (Complete Specification)
-----------------------------------------

Block specific HTTPS connection:

.. code-block:: json

   {
       "blocking_rule": {
           "protocol": "tcp",
           "local_port": 443,
           "remote_ip": "***********/24",
           "remote_port": 8443,
           "direction": "ingress",
           "description": "Block specific HTTPS connection"
       }
   }