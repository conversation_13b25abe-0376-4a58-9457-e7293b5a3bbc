#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc

from neutron_lib.api import extensions as api_extensions
from neutron_lib.api import validators
from neutron_lib.services import base as service_base
import six

from neutron._i18n import _
from neutron.api.v2 import resource_helper
from neutron.extensions import _route_table as apidef


def validate_routetableroutes(data, valid_values=None):
    expected_keys = ['destination', 'nexthop', 'type']
    ruotetableruotes = []
    for route in data:
        msg = validators._verify_dict_keys(expected_keys, route)
        if msg:
            return msg
        msg = validators.validate_subnet(route['destination'])
        if msg:
            return msg
        msg = validators.validate_ip_address(route['nexthop'])
        if msg:
            return msg
        msg = validators.validate_string(route['type'], 255)
        if msg:
            return msg
        if route in ruotetableruotes:
            msg = _("Duplicate route '%s'") % route
            return msg
        ruotetableruotes.append(route)


validators.add_validator('routetableroutes', validate_routetableroutes)


class Route_table(api_extensions.APIExtensionDescriptor):
    """ROUTE TABLE API extension."""

    api_definition = apidef

    @classmethod
    def get_resources(cls):
        """Returns Ext Resources."""
        plural_mappings = resource_helper.build_plural_mappings(
            {}, apidef.RESOURCE_ATTRIBUTE_MAP)
        return resource_helper.build_resource_info(
            plural_mappings, apidef.RESOURCE_ATTRIBUTE_MAP,
            apidef.ROUTE_TABLE, action_map=apidef.ACTION_MAP)

    @classmethod
    def get_plugin_interface(cls):
        return RouteTablePluginBase


@six.add_metaclass(abc.ABCMeta)
class RouteTablePluginBase(service_base.ServicePluginBase):

    @classmethod
    def get_plugin_type(cls):
        return apidef.ROUTE_TABLE

    def get_plugin_description(self):
        return "Route table Service Plugin"

    @abc.abstractmethod
    def create_route_table(self, context, route_table):
        pass

    @abc.abstractmethod
    def update_route_table(self, context, id, route_table):
        pass

    @abc.abstractmethod
    def get_route_table(self, context, id, fields=None):
        pass

    @abc.abstractmethod
    def get_route_tables(self, context, filters=None, fields=None, sorts=None,
                         limit=None, marker=None, page_reverse=False):
        pass

    @abc.abstractmethod
    def delete_route_table(self, context, id):
        pass

    @abc.abstractmethod
    def add_route_table_routes(self, context, id, route_table):
        pass

    @abc.abstractmethod
    def update_route_table_routes(self, context, id, route_table):
        pass

    @abc.abstractmethod
    def remove_route_table_routes(self, context, id, route_table):
        pass

    @abc.abstractmethod
    def associate_subnets(self, context, id, route_table):
        pass

    @abc.abstractmethod
    def disassociate_subnets(self, context, id, route_table):
        pass

    @abc.abstractmethod
    def associate_gateway(self, context, id):
        pass

    @abc.abstractmethod
    def disassociate_gateway(self, context, id):
        pass
