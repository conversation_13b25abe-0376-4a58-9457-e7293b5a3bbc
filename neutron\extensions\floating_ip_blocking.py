#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import abc
import itertools

from neutron_lib.api import extensions as api_extensions
from neutron_lib import exceptions as n_exc
from neutron_lib.plugins import directory
from neutron_lib.services import base as service_base
import six

from neutron.api import extensions
from neutron.api.v2 import base
from neutron.api.v2 import resource_helper
from neutron.extensions import _floating_ip_blocking as apidef
from neutron import policy
from neutron._i18n import _
from neutron.services.floating_ip_blocking.common import exceptions as fip_exceptions

from oslo_log import log as logging
from webob import exc as web_exc

LOG = logging.getLogger(__name__)


def convert_exception_to_http_exc(e):
    """Convert floating IP blocking exceptions to HTTP exceptions."""
    status_code = fip_exceptions.get_http_status_code(e)
    error_response = fip_exceptions.format_error_response(e, include_details=True)
    
    # Map status codes to WebOb exceptions
    if status_code == 400:
        return web_exc.HTTPBadRequest(json_body=error_response)
    elif status_code == 404:
        return web_exc.HTTPNotFound(json_body=error_response)
    elif status_code == 409:
        return web_exc.HTTPConflict(json_body=error_response)
    elif status_code == 413:
        return web_exc.HTTPRequestEntityTooLarge(json_body=error_response)
    elif status_code == 422:
        return web_exc.HTTPUnprocessableEntity(json_body=error_response)
    elif status_code == 503:
        return web_exc.HTTPServiceUnavailable(json_body=error_response)
    else:
        return web_exc.HTTPInternalServerError(json_body=error_response)


def handle_floating_ip_blocking_exceptions(func):
    """Decorator to handle floating IP blocking exceptions in API layer."""
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except fip_exceptions.FloatingIPBlockingException as e:
            # Log the exception with context
            context = args[1] if len(args) > 1 else None
            fip_exceptions.log_exception_details(e, context=context)
            # Convert to HTTP exception
            raise convert_exception_to_http_exc(e)
        except Exception as e:
            # Handle unexpected exceptions
            LOG.exception("Unexpected error in floating IP blocking API")
            error_response = {
                'error': {
                    'type': 'InternalServerError',
                    'message': 'An unexpected error occurred',
                    'code': 500
                }
            }
            raise web_exc.HTTPInternalServerError(json_body=error_response)
    return wrapper


def validate_blocking_rule(rule_data):
    """Validate blocking rule data.
    
    :param rule_data: Dictionary containing rule data
    :raises: InvalidBlockingRule if validation fails
    """
    try:
        # Use the enhanced validation from exceptions module
        fip_exceptions.validate_and_raise_if_invalid(
            _validate_rule_fields, rule_data)
    except fip_exceptions.BlockingRuleValidationError as e:
        raise fip_exceptions.InvalidBlockingRule(reason=str(e))


def _validate_rule_fields(rule_data):
    """Internal validation logic for rule fields."""
    # Validate that at least one field is specified for meaningful rule
    meaningful_fields = ['protocol', 'local_port', 'remote_ip', 'remote_port']
    if not any(rule_data.get(field) for field in meaningful_fields):
        raise fip_exceptions.InvalidBlockingRule(
            reason=_("At least one of protocol, local_port, remote_ip, "
                    "or remote_port must be specified"))
    
    # Validate protocol-port combinations
    protocol = rule_data.get('protocol')
    local_port = rule_data.get('local_port')
    remote_port = rule_data.get('remote_port')
    
    if protocol == 'icmp' and (local_port or remote_port):
        raise fip_exceptions.InvalidBlockingRule(
            reason=_("ICMP protocol cannot have port specifications"))
    
    if protocol in ['tcp', 'udp'] and not (local_port or remote_port):
        # This is allowed - can block all TCP/UDP traffic
        pass
    
    # Validate direction is required
    if not rule_data.get('direction'):
        raise fip_exceptions.InvalidBlockingRule(
            reason=_("Direction must be specified"))
    
    return True


def validate_rule_update(original_rule, updated_rule):
    """Validate rule update data.
    
    :param original_rule: Original rule data
    :param updated_rule: Updated rule data
    :raises: InvalidBlockingRule if validation fails
    """
    # Ensure floatingip_id cannot be changed
    if ('floatingip_id' in updated_rule and 
        updated_rule['floatingip_id'] != original_rule['floatingip_id']):
        raise fip_exceptions.InvalidBlockingRule(
            reason=_("Cannot change floatingip_id of existing rule"))
    
    # Validate the updated rule
    merged_rule = original_rule.copy()
    merged_rule.update(updated_rule)
    validate_blocking_rule(merged_rule)


def log_rule_operation(context, operation, floatingip_id, rule_id=None, 
                      rule_data=None, error=None):
    """Log blocking rule operations for audit purposes.
    
    :param context: neutron context
    :param operation: operation type (create, update, delete, get)
    :param floatingip_id: UUID of the floating IP
    :param rule_id: UUID of the blocking rule (optional)
    :param rule_data: rule data for create/update operations (optional)
    :param error: error message if operation failed (optional)
    """
    user_id = getattr(context, 'user_id', 'unknown')
    project_id = getattr(context, 'project_id', 'unknown')
    
    log_data = {
        'operation': operation,
        'user_id': user_id,
        'project_id': project_id,
        'floatingip_id': floatingip_id,
        'rule_id': rule_id,
    }
    
    if rule_data:
        # Log only essential rule data for security
        safe_rule_data = {
            'protocol': rule_data.get('protocol'),
            'direction': rule_data.get('direction'),
            'local_port': rule_data.get('local_port'),
            'remote_port': rule_data.get('remote_port'),
            # Don't log remote_ip for privacy
        }
        log_data['rule_data'] = safe_rule_data
    
    if error:
        # Use enhanced exception logging
        if isinstance(error, fip_exceptions.FloatingIPBlockingException):
            fip_exceptions.log_exception_details(
                error, context=context, **log_data)
        else:
            LOG.warning("Floating IP blocking rule operation failed: "
                       "%(log_data)s, error: %(error)s", 
                       {'log_data': log_data, 'error': error})
    else:
        LOG.info("Floating IP blocking rule operation: %(log_data)s", 
                {'log_data': log_data})


class Floating_ip_blocking(api_extensions.APIExtensionDescriptor):
    """Floating IP Traffic Blocking API extension."""

    api_definition = apidef

    @classmethod
    def get_resources(cls):
        """Returns Ext Resources."""
        special_mappings = {'floatingips': 'floatingip'}
        plural_mappings = resource_helper.build_plural_mappings(
            special_mappings, itertools.chain(
                apidef.RESOURCE_ATTRIBUTE_MAP,
                apidef.SUB_RESOURCE_ATTRIBUTE_MAP))

        resources = resource_helper.build_resource_info(
                plural_mappings,
                apidef.RESOURCE_ATTRIBUTE_MAP,
                apidef.FLOATING_IP_BLOCKING,
                translate_name=True,
                allow_bulk=True)

        plugin = directory.get_plugin(apidef.FLOATING_IP_BLOCKING)

        parent = apidef.SUB_RESOURCE_ATTRIBUTE_MAP[
            apidef.COLLECTION_NAME].get('parent')
        params = apidef.SUB_RESOURCE_ATTRIBUTE_MAP[apidef.COLLECTION_NAME].get(
            'parameters')

        controller = base.create_resource(apidef.COLLECTION_NAME,
                                          apidef.RESOURCE_NAME,
                                          plugin, params,
                                          allow_bulk=True,
                                          parent=parent,
                                          allow_pagination=True,
                                          allow_sorting=True)

        resource = extensions.ResourceExtension(
            apidef.COLLECTION_NAME,
            controller, parent,
            attr_map=params)
        resources.append(resource)

        return resources

    @classmethod
    def get_plugin_interface(cls):
        return FloatingIPBlockingPluginBase


@six.add_metaclass(abc.ABCMeta)
class FloatingIPBlockingPluginBase(service_base.ServicePluginBase):
    """Base class for Floating IP Blocking service plugin."""

    path_prefix = apidef.API_PREFIX

    @classmethod
    def get_plugin_type(cls):
        return apidef.FLOATING_IP_BLOCKING

    def get_plugin_description(self):
        return "Floating IP Traffic Blocking Service Plugin"

    @abc.abstractmethod
    @handle_floating_ip_blocking_exceptions
    def create_floatingip_blocking_rule(self, context, floatingip_id, 
                                       blocking_rule):
        """Create a blocking rule for a floating IP.
        
        :param context: neutron context
        :param floatingip_id: UUID of the floating IP
        :param blocking_rule: blocking rule data
        :returns: created blocking rule dict
        :raises: InvalidBlockingRule, FloatingIPNotFound, BlockingRuleConflict
        
        Implementation should:
        1. Enforce policy: create_floatingip_blocking_rule
        2. Validate rule data using validate_blocking_rule()
        3. Check floating IP exists and user has access
        4. Detect and handle rule conflicts
        5. Create and return the rule
        6. Log operation using log_rule_operation()
        """
        pass

    @abc.abstractmethod
    @handle_floating_ip_blocking_exceptions
    def update_floatingip_blocking_rule(self, context, id, floatingip_id, 
                                       blocking_rule):
        """Update a blocking rule.
        
        :param context: neutron context
        :param id: UUID of the blocking rule
        :param floatingip_id: UUID of the floating IP
        :param blocking_rule: blocking rule data
        :returns: updated blocking rule dict
        :raises: InvalidBlockingRule, BlockingRuleNotFound, BlockingRuleConflict
        
        Implementation should:
        1. Enforce policy: update_floatingip_blocking_rule
        2. Get original rule and validate update using validate_rule_update()
        3. Check for conflicts with other rules
        4. Update and return the rule
        5. Log operation using log_rule_operation()
        """
        pass

    @abc.abstractmethod
    @handle_floating_ip_blocking_exceptions
    def get_floatingip_blocking_rule(self, context, id, floatingip_id, 
                                    fields=None):
        """Get a specific blocking rule.
        
        :param context: neutron context
        :param id: UUID of the blocking rule
        :param floatingip_id: UUID of the floating IP
        :param fields: list of fields to return
        :returns: blocking rule dict
        :raises: BlockingRuleNotFound, FloatingIPNotFound
        
        Implementation should:
        1. Enforce policy: get_floatingip_blocking_rule
        2. Verify floating IP exists and user has access
        3. Return the requested rule
        4. Log operation using log_rule_operation()
        """
        pass

    @abc.abstractmethod
    @handle_floating_ip_blocking_exceptions
    def get_floatingip_blocking_rules(self, context, floatingip_id=None, 
                                     filters=None, fields=None, sorts=None, 
                                     limit=None, marker=None, 
                                     page_reverse=False):
        """Get blocking rules for a floating IP.
        
        :param context: neutron context
        :param floatingip_id: UUID of the floating IP
        :param filters: filters to apply
        :param fields: list of fields to return
        :param sorts: list of sort keys and directions
        :param limit: maximum number of items to return
        :param marker: marker for pagination
        :param page_reverse: reverse pagination
        :returns: list of blocking rule dicts
        :raises: FloatingIPNotFound
        
        Implementation should:
        1. Enforce policy: get_floatingip_blocking_rules
        2. Verify floating IP exists and user has access
        3. Apply filters and return matching rules
        4. Log operation using log_rule_operation()
        """
        pass

    @abc.abstractmethod
    @handle_floating_ip_blocking_exceptions
    def delete_floatingip_blocking_rule(self, context, id, floatingip_id):
        """Delete a blocking rule.
        
        :param context: neutron context
        :param id: UUID of the blocking rule
        :param floatingip_id: UUID of the floating IP
        :raises: BlockingRuleNotFound, FloatingIPNotFound
        
        Implementation should:
        1. Enforce policy: delete_floatingip_blocking_rule
        2. Verify rule exists and user has access
        3. Delete the rule and clean up empty blocking table if needed
        4. Log operation using log_rule_operation()
        """
        pass