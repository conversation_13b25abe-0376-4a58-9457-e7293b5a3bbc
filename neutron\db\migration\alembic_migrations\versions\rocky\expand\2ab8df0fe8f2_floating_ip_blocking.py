#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

"""floating_ip_blocking

Revision ID: 2ab8df0fe8f2
Revises: 19fbe82a8503
Create Date: 2025-01-19 10:00:00.000000

"""

from alembic import op
import sqlalchemy as sa

from neutron_lib.db import constants

# revision identifiers, used by Alembic.
revision = '2ab8df0fe8f2'
down_revision = '19fbe82a8503'


def upgrade():
    # Create floatingip_blocking_tables table
    op.create_table(
        'floatingip_blocking_tables',
        sa.Column('id', sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('standard_attr_id', sa.BigInteger(), nullable=False),
        sa.Column('project_id',
                  sa.String(length=constants.PROJECT_ID_FIELD_SIZE),
                  nullable=False,
                  index=True),
        sa.Column('floatingip_id',
                  sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('enabled', sa.Boolean(), nullable=False, default=True),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(['floatingip_id'], ['floatingips.id'],
                                ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['standard_attr_id'], ['standardattributes.id'],
                                ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('floatingip_id',
                            name='uniq_floatingip_blocking_tables0floatingip_id'),
    )

    # Create floatingip_blocking_rules table
    op.create_table(
        'floatingip_blocking_rules',
        sa.Column('id', sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('standard_attr_id', sa.BigInteger(), nullable=False),
        sa.Column('blocking_table_id',
                  sa.String(length=constants.UUID_FIELD_SIZE),
                  nullable=False),
        sa.Column('protocol', sa.String(length=10), nullable=True),
        sa.Column('local_port', sa.Integer(), nullable=True),
        sa.Column('remote_ip', sa.String(length=64), nullable=True),
        sa.Column('remote_port', sa.Integer(), nullable=True),
        sa.Column('direction', sa.Enum('ingress', 'egress',
                                       name='traffic_direction'),
                  nullable=False),
        sa.Column('description', sa.String(length=255), nullable=True),
        sa.ForeignKeyConstraint(['blocking_table_id'],
                                ['floatingip_blocking_tables.id'],
                                ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['standard_attr_id'], ['standardattributes.id'],
                                ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('blocking_table_id', 'protocol', 'local_port',
                            'remote_ip', 'remote_port', 'direction',
                            name='uniq_floatingip_blocking_rule0'
                                 'blocking_table_id0protocol0local_port0'
                                 'remote_ip0remote_port0direction'),
    )
