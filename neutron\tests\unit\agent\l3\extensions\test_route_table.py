#
#    Licensed under the Apache License, Version 2.0 (the "License"); you may
#    not use this file except in compliance with the License. You may obtain
#    a copy of the License at
#
#         http://www.apache.org/licenses/LICENSE-2.0
#
#    Unless required by applicable law or agreed to in writing, software
#    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
#    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied. See the
#    License for the specific language governing permissions and limitations
#    under the License.

import mock
from neutron_lib import constants as lib_constants
from neutron_lib import context
from oslo_utils import uuidutils

from neutron.agent.l3 import agent as l3_agent
from neutron.agent.l3.extensions import route_table
from neutron.agent.l3 import l3_agent_extension_api as l3_ext_api
from neutron.agent.l3 import router_info as l3router
from neutron.agent.linux import iptables_manager
from neutron.tests.unit.agent.l3 import test_agent


_uuid = uuidutils.generate_uuid
HOSTNAME = 'testhost'


class RouteTableAgentExtensionBase(
    test_agent.BasicRouterOperationsFramework):

    def setUp(self):
        super(RouteTableAgentExtensionBase, self).setUp()

        self.route_table_ext = route_table.RouteTableAgentExtension()
        self.ctx = context.get_admin_context()
        self.connection = mock.Mock()
        self.agent = l3_agent.L3NATAgent(HOSTNAME, self.conf)

        self.router_id = _uuid()
        self.gateway_port_id = _uuid()
        self.ex_gw_port = {'id': self.gateway_port_id,
                           'fixed_ips': [{'ip_address': '**********',
                                          'visible': True,
                                          'prefixlen': 24}],
                           'subnets': [{
                               "gateway_ip": "********",
                               "cidr": "10.0.0.0/24"}]}
        self.subnet_1_uuid = _uuid()
        self.subnet_2_uuid = _uuid()
        self._interfaces = [
            {"id": _uuid(),
             "subnets": [{"gateway_ip": "*********",
                          "cidr": "100.0.0.0/24",
                          "id": self.subnet_1_uuid}],
             "fixed_ips": [{"visible": True,
                            "prefixlen": 24,
                            "ip_address": "*********"}]},
            {"id": _uuid(),
             "subnets": [{"gateway_ip": "*********",
                          "cidr": "200.0.0.0/24",
                          "id": self.subnet_2_uuid}],
             "fixed_ips": [{"visible": True,
                            "prefixlen": 24,
                            "ip_address": "*********"}]}]

        self.default_route_table = {
            'id': _uuid(), 'name': 'rt', 'router_id': self.router_id,
            'table_id': 200, 'associate_type': 'subnet'}

        self.default_rt_default_routes = [
            {'type': 'default', 'destination': '0.0.0.0/0',
            'nexthop': '********', 'routetable_id':
            self.default_route_table['id']}]

        self.default_rt_direct_routes = [
            {'type': 'system_direct', 'destination': '100.0.0.0/24',
            'nexthop': '*********', 'routetable_id':
            self.default_route_table['id']},
            {'type': 'system_direct', 'destination': '200.0.0.0/24',
            'nexthop': '*********', 'routetable_id':
            self.default_route_table['id']}]

        self.subnet_route_table = {
            'id': _uuid(), 'name': 'rt', 'router_id': self.router_id,
            'table_id': 201, 'associate_type': 'subnet'}

        self.subnet_rt_default_routes = [
            {'type': 'default', 'destination': '0.0.0.0/0',
            'nexthop': '********', 'routetable_id':
            self.subnet_route_table['id']}]

        self.subnet_rt_direct_routes = [
            {'type': 'system_direct', 'destination': '100.0.0.0/24',
            'nexthop': '*********', 'routetable_id':
            self.subnet_route_table['id']},
            {'type': 'system_direct', 'destination': '200.0.0.0/24',
            'nexthop': '*********', 'routetable_id':
            self.subnet_route_table['id']}]

        self.subnet_rt_custom_routes = [
            {'type': 'ecs', 'destination': '120.0.0.0/24',
             'nexthop': '*********0', 'routetable_id':
             self.subnet_route_table['id']}]

        self.gateway_route_table = {
            'id': _uuid(), 'name': 'rt', 'router_id': self.router_id,
            'table_id': 202, 'associate_type': 'gateway'}

        self.gateway_rt_custom_routes = [
            {'type': 'ecs', 'destination': '100.0.0.0/24',
            'nexthop': '**********', 'routetable_id':
            self.gateway_route_table['id']}]

        self.subnet_bindings = [
            {'subnet_id': self.subnet_1_uuid,
             'routetable_id': self.default_route_table['id'],
             'cidr': '100.0.0.0/24'},
            {'subnet_id': self.subnet_2_uuid,
             'routetable_id': self.default_route_table['id'],
             'cidr': '200.0.0.0/24'}]
        self.gateway_binding = {}

        self.router = {'id': self.router_id,
                       'gw_port': self.ex_gw_port,
                       '_interfaces': self._interfaces,
                       'ha': False,
                       'distributed': False,
                       'rt_id_and_table_id_pairs': {
                           self.default_route_table['id']:
                               self.default_route_table['table_id'],
                           self.subnet_route_table['id']:
                               self.subnet_route_table['table_id'],
                           self.gateway_route_table['id']:
                               self.gateway_route_table['table_id']},
                       'default_route_table_id': self.default_route_table[
                           'id'],
                       'route_table_routes': (
                           self.default_rt_default_routes +
                           self.default_rt_direct_routes +
                           self.subnet_rt_default_routes +
                           self.subnet_rt_direct_routes +
                           self.subnet_rt_custom_routes +
                           self.gateway_rt_custom_routes),
                       'subnet_route_table_bindings': self.subnet_bindings,
                       'gateway_route_table_binding': self.gateway_binding}

        self.router_info = l3router.RouterInfo(
            self.agent, self.router_id, self.router,
            **self.ri_kwargs)
        self.router_info.ex_gw_port = self.ex_gw_port
        self.router_info.internal_ports = self._interfaces
        self.agent.router_info[self.router['id']] = self.router_info

        def _get_router_info(router_id):
            return self.agent.router_info.get(router_id)

        self.get_router_info = mock.patch(
            'neutron.agent.l3.l3_agent_extension_api.'
            'L3AgentExtensionAPI.get_router_info').start()
        self.get_router_info.side_effect = _get_router_info
        self.agent_api = l3_ext_api.L3AgentExtensionAPI(None, None)
        self.route_table_ext.consume_api(self.agent_api)

        self.mock_add_rule = mock.patch.object(
            iptables_manager.IptablesTable, 'add_rule').start()


class RouteTableAgentExtensionTestCase(RouteTableAgentExtensionBase):

    def setUp(self):
        super(RouteTableAgentExtensionTestCase, self).setUp()
        self.route_table_ext.initialize(
            self.connection, lib_constants.L3_AGENT_MODE)

    def _check_agent_method_called(self, calls):
        self.mock_ip.netns.execute.assert_has_calls(
            [mock.call(call, log_fail_as_error=False) for call in calls],
            any_order=True)

    def _reset_mock(self):
        self.mock_add_rule.reset_mock()
        self.mock_add_ip_rule.reset_mock()
        self.mock_ip.reset_mock()
        self.mock_ip_dev.route.add_route.reset_mock()
        self.mock_ip_dev.route.add_gateway.reset_mock()

    def _update_router(self, router):
        self._reset_mock()
        router_info = l3router.RouterInfo(
            self.agent, self.router_id, router,
            **self.ri_kwargs)
        router_info.ex_gw_port = self.ex_gw_port
        router_info.internal_ports = self._interfaces
        self.agent.router_info[self.router_id] = router_info

        def _get_router_info(router_id):
            return self.agent.router_info.get(router_id)

        self.get_router_info.side_effect = _get_router_info
        self.route_table_ext.update_router(self.ctx, router)

    def test_create_custom_route_tables_with_custom_routes(self):
        namespace = self.router_info.router_namespace.name
        # Add router with new route table routes
        self.route_table_ext.add_router(self.ctx, self.router)

        # Setup system direct routes
        expected = [mock.call('100.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=200),
                    mock.call('200.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=200),
                    mock.call('100.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=201),
                    mock.call('200.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=201)]
        self.mock_ip_dev.route.add_route.assert_has_calls(
            expected, any_order=True)

        # Setup default routes
        expected = [['ip', 'route', 'replace', 'to', '0.0.0.0/0',
                     'via', '********', 'table', 200],
                    ['ip', 'route', 'replace', 'to', '0.0.0.0/0',
                     'via', '********', 'table', 201]]
        self._check_agent_method_called(expected)

        # Setup custom route table routes
        expected = [['ip', 'route', 'replace', 'to', '120.0.0.0/24',
                     'via', '*********0', 'table', 201],
                    ['ip', 'route', 'replace', 'to', '100.0.0.0/24',
                     'via', '**********', 'table', 202]]
        self._check_agent_method_called(expected)

        # Setup default route table and subnets associations
        ri = self.router_info
        dev0 = ri.get_internal_device_name(self._interfaces[0]['id'])
        dev1 = ri.get_internal_device_name(self._interfaces[1]['id'])
        expected_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=200,
                      priority=100),
            mock.call(namespace, '0.0.0.0/0', iif=dev1, table=200,
                      priority=100)]
        self.mock_add_ip_rule.assert_has_calls(expected_rules, any_order=True)

        # Update route table routes
        subnet_rt_custom_routes = [
            {'type': 'ecs', 'destination': '120.0.0.0/24',
             'nexthop': '*********1', 'routetable_id':
             self.subnet_route_table['id']}]

        gateway_rt_custom_routes = [
            {'type': 'ecs', 'destination': '100.0.0.0/24',
            'nexthop': '**********', 'routetable_id':
            self.gateway_route_table['id']}]

        router = self.router
        router['route_table_routes'] = (
            self.default_rt_default_routes +
            self.default_rt_direct_routes +
            self.subnet_rt_default_routes +
            self.subnet_rt_direct_routes +
            subnet_rt_custom_routes +
            gateway_rt_custom_routes)
        self._update_router(router)
        expected = [['ip', 'route', 'replace', 'to', '120.0.0.0/24',
                     'via', '*********1', 'table', 201],
                    ['ip', 'route', 'replace', 'to', '100.0.0.0/24',
                     'via', '**********', 'table', 202]]
        self._check_agent_method_called(expected)

        # Delete route table routes
        router = self.router
        router['route_table_routes'] = (
            self.default_rt_default_routes +
            self.default_rt_direct_routes +
            self.subnet_rt_default_routes +
            self.subnet_rt_direct_routes)
        self._update_router(router)
        expected = [['ip', 'route', 'delete', 'to', '120.0.0.0/24',
                     'via', '*********1', 'table', 201],
                    ['ip', 'route', 'delete', 'to', '100.0.0.0/24',
                     'via', '**********', 'table', 202]]
        self._check_agent_method_called(expected)

    def test_associate_and_disassociate_rt_with_custom_default_routes(self):
        # Add router with new route table routes
        self.route_table_ext.add_router(self.ctx, self.router)
        router = self.router
        # Update subnet route table routes by updating custom default route.
        subnet_rt_custom_routes = [
            {'type': 'ecs', 'destination': '0.0.0.0/0',
             'nexthop': '**********', 'routetable_id':
             self.subnet_route_table['id']}]

        router['route_table_routes'] = (
            self.default_rt_default_routes +
            self.default_rt_direct_routes +
            self.subnet_rt_default_routes +
            self.subnet_rt_direct_routes +
            subnet_rt_custom_routes +
            self.gateway_rt_custom_routes)

        # Associate subnet to subnet route table, and associate gateway to
        # gateway route table.
        subnet_bindings = [
            {'subnet_id': self.subnet_1_uuid,
             'routetable_id': self.subnet_route_table['id'],
             'cidr': '100.0.0.0/24'},
            {'subnet_id': self.subnet_2_uuid,
             'routetable_id': self.default_route_table['id'],
             'cidr': '200.0.0.0/24'}]
        gateway_binding = {'router_id': self.router_id,
                           'routetable_id': self.gateway_route_table['id'],
                           'cidr': '100.0.0.0/24'}
        router['subnet_route_table_bindings'] = subnet_bindings
        router['gateway_route_table_binding'] = gateway_binding
        self._update_router(router)

        # Setup default route table and subnets associations
        namespace = self.router_info.router_namespace.name
        ri = self.router_info
        dev0 = ri.get_internal_device_name(self._interfaces[0]['id'])
        dev1 = ri.get_internal_device_name(self._interfaces[1]['id'])
        ext_dev = ri.get_external_device_name(self.ex_gw_port['id'])
        expected_delete_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=200,
                      priority=100)]
        expected_add_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=201,
                      priority=100),
            mock.call(namespace, '0.0.0.0/0', iif=ext_dev, table=202,
                      priority=50)]

        self.mock_delete_ip_rule.assert_has_calls(expected_delete_rules,
                                                  any_order=True)
        self.mock_add_ip_rule.assert_has_calls(expected_add_rules,
                                               any_order=True)

        # iptables rules of raw table
        self.mock_add_rule.assert_has_calls(
            [mock.call(route_table.PREROUTING_ACCEPT_CHAIN,
             '-d ***************/32 -i %(dev0)s -j ACCEPT' % {"dev0": dev0}),
             mock.call(route_table.PREROUTING_ACCEPT_CHAIN,
             '-d 100.0.0.0/24 -i %(dev0)s -j ACCEPT' % {"dev0": dev0}),
             mock.call(route_table.PREROUTING_ACCEPT_CHAIN,
             '-d 200.0.0.0/24 -i %(dev0)s -j ACCEPT' % {"dev0": dev0}),
             mock.call(route_table.PREROUTING_NOTRACK_CHAIN,
             '-i %(dev0)s -j NOTRACK' % {"dev0": dev0}),
             mock.call(route_table.PREROUTING_NOTRACK_CHAIN,
             '! -s 200.0.0.0/24 -d 100.0.0.0/24 -i %(dev1)s -j NOTRACK' % {
                 "dev1": dev1})])

        # Disassociate subnet from subnet route table, and disassociate
        # gateway from gateway route table.
        subnet_bindings = [
            {'subnet_id': self.subnet_1_uuid,
             'routetable_id': self.default_route_table['id'],
             'cidr': '100.0.0.0/24'},
            {'subnet_id': self.subnet_2_uuid,
             'routetable_id': self.default_route_table['id'],
             'cidr': '200.0.0.0/24'}]
        gateway_binding = {}
        router = self.router
        router['subnet_route_table_bindings'] = subnet_bindings
        router['gateway_route_table_binding'] = gateway_binding
        self._update_router(router)
        # Setup default route table and subnets associations
        ri = self.router_info
        dev0 = ri.get_internal_device_name(self._interfaces[0]['id'])
        ext_dev = ri.get_external_device_name(self.ex_gw_port['id'])
        expected_delete_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=201,
                      priority=100),
            mock.call(namespace, '0.0.0.0/0', iif=ext_dev, table=202,
                      priority=50)]
        expected_add_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=200,
                      priority=100)]

        self.mock_delete_ip_rule.assert_has_calls(expected_delete_rules,
                                                  any_order=True)
        self.mock_add_ip_rule.assert_has_calls(expected_add_rules,
                                               any_order=True)

    def test_associate_route_table_with_custom_user_routes(self):
        # Add router with new route table routes
        self.route_table_ext.add_router(self.ctx, self.router)
        router = self.router

        # Update subnet route table routes by adding custom routes.
        subnet_rt_custom_routes = [
            {'type': 'ecs', 'destination': '*******/32',
             'nexthop': '**********', 'routetable_id':
             self.subnet_route_table['id']},
            {'type': 'ecs', 'destination': '*******/32',
             'nexthop': '**********', 'routetable_id':
             self.subnet_route_table['id']}]

        router['route_table_routes'] = (
            self.default_rt_default_routes +
            self.default_rt_direct_routes +
            self.subnet_rt_default_routes +
            self.subnet_rt_direct_routes +
            subnet_rt_custom_routes +
            self.gateway_rt_custom_routes)

        # Associate subnet to subnet route table.
        subnet_bindings = [
            {'subnet_id': self.subnet_1_uuid,
             'routetable_id': self.subnet_route_table['id'],
             'cidr': '100.0.0.0/24'},
            {'subnet_id': self.subnet_2_uuid,
             'routetable_id': self.default_route_table['id'],
             'cidr': '200.0.0.0/24'}]
        router['subnet_route_table_bindings'] = subnet_bindings

        self._update_router(router)

        # Setup default route table and subnets associations
        ri = self.router_info
        dev0 = ri.get_internal_device_name(self._interfaces[0]['id'])

        # iptables rules of raw table
        self.mock_add_rule.assert_has_calls(
            [mock.call(route_table.PREROUTING_NOTRACK_CHAIN,
             '-d *******/32 -i %(dev0)s -j NOTRACK' % {"dev0": dev0}),
             mock.call(route_table.PREROUTING_NOTRACK_CHAIN,
             '-d *******/32 -i %(dev0)s -j NOTRACK' % {"dev0": dev0})])

    def test_associate_route_table_with_custom_default_and_user_routes(self):
        # Add router with new route table routes
        self.route_table_ext.add_router(self.ctx, self.router)
        router = self.router

        # Update subnet route table routes by adding custom routes.
        subnet_rt_custom_routes = [
            {'type': 'ecs', 'destination': '0.0.0.0/0',
             'nexthop': '**********', 'routetable_id':
             self.subnet_route_table['id']},
            {'type': 'ecs', 'destination': '*******/32',
             'nexthop': '**********', 'routetable_id':
             self.subnet_route_table['id']},
            {'type': 'ecs', 'destination': '*******/32',
             'nexthop': '**********', 'routetable_id':
             self.subnet_route_table['id']}]

        router['route_table_routes'] = (
            self.default_rt_default_routes +
            self.default_rt_direct_routes +
            self.subnet_rt_default_routes +
            self.subnet_rt_direct_routes +
            subnet_rt_custom_routes +
            self.gateway_rt_custom_routes)

        # Associate subnet to subnet route table.
        subnet_bindings = [
            {'subnet_id': self.subnet_1_uuid,
             'routetable_id': self.subnet_route_table['id'],
             'cidr': '100.0.0.0/24'},
            {'subnet_id': self.subnet_2_uuid,
             'routetable_id': self.default_route_table['id'],
             'cidr': '200.0.0.0/24'}]
        router['subnet_route_table_bindings'] = subnet_bindings

        self._update_router(router)

        # Setup default route table and subnets associations
        ri = self.router_info
        dev0 = ri.get_internal_device_name(self._interfaces[0]['id'])

        # iptables rules of raw table
        self.mock_add_rule.assert_has_calls(
            [mock.call(route_table.PREROUTING_ACCEPT_CHAIN,
             '-d ***************/32 -i %(dev0)s -j ACCEPT' % {"dev0": dev0}),
             mock.call(route_table.PREROUTING_ACCEPT_CHAIN,
             '-d 100.0.0.0/24 -i %(dev0)s -j ACCEPT' % {"dev0": dev0}),
             mock.call(route_table.PREROUTING_ACCEPT_CHAIN,
             '-d 200.0.0.0/24 -i %(dev0)s -j ACCEPT' % {"dev0": dev0}),
             mock.call(route_table.PREROUTING_NOTRACK_CHAIN,
             '-i %(dev0)s -j NOTRACK' % {"dev0": dev0})])

    def test_delete_route_table_when_route_table_has_cunsom_routes(self):
        # Add router with new route table routes
        self.route_table_ext.add_router(self.ctx, self.router)

        # Delete subnet route table and gateway route table
        router = self.router
        router['rt_id_and_table_id_pairs'] = {
            self.default_route_table['id']:
            self.default_route_table['table_id']}
        router['route_table_routes'] = (
            self.default_rt_default_routes + self.default_rt_direct_routes)
        router['subnet_route_table_bindings'] = []
        router['gateway_route_table_binding'] = {}
        self._update_router(router)

        # Delete system direct routes
        expected = [mock.call('100.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=201),
                    mock.call('200.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=201)]
        self.mock_ip_dev.route.delete_route.assert_has_calls(
            expected, any_order=True)

        # Delete default routes
        expected = [mock.call('********', table=201)]
        self.mock_ip_dev.route.delete_gateway.assert_has_calls(
            expected, any_order=True)

        # Delete custom route table routes
        expected = [['ip', 'route', 'delete', 'to', '120.0.0.0/24',
                     'via', '*********0', 'table', 201],
                    ['ip', 'route', 'delete', 'to', '100.0.0.0/24',
                     'via', '**********', 'table', 202]]
        self._check_agent_method_called(expected)

    def test_router_remove_subnet_and_external_gateway(self):
        # Add router with new route table routes
        self.route_table_ext.add_router(self.ctx, self.router)

        # Remove subnet and external gateway from router
        router = self.router
        router['gw_port'] = None
        router['_interfaces'] = []
        router['rt_id_and_table_id_pairs'] = {
            self.default_route_table['id']:
            self.default_route_table['table_id']}
        router['route_table_routes'] = []
        router['subnet_route_table_bindings'] = []

        def device_exists():
            return False
        self.mock_ip_dev.exists.side_effect = device_exists
        self._update_router(router)

        # Remove subnet and external gateway from router will delete qr- and
        # qg- port, the system direct route and default route will be deleted
        # automatically.
        self.mock_ip_dev.route.delete_route.assert_not_called()
        self.mock_ip_dev.route.delete_gateway.assert_not_called()

        # Delete default route table and subnets associations
        namespace = self.router_info.router_namespace.name
        ri = self.router_info
        dev0 = ri.get_internal_device_name(self._interfaces[0]['id'])
        dev1 = ri.get_internal_device_name(self._interfaces[1]['id'])
        expected_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=200,
                      priority=100),
            mock.call(namespace, '0.0.0.0/0', iif=dev1, table=200,
                      priority=100)]
        self.mock_delete_ip_rule.assert_has_calls(expected_rules,
                                                  any_order=True)

        # Delete custom route table routes
        expected = [['ip', 'route', 'delete', 'to', '120.0.0.0/24',
                     'via', '*********0', 'table', 201],
                    ['ip', 'route', 'delete', 'to', '100.0.0.0/24',
                     'via', '**********', 'table', 202]]
        self._check_agent_method_called(expected)

    def test_route_remove_subnet_while_subnet_associated_to_route_table(self):
        namespace = self.router_info.router_namespace.name
        # Add router with new route table routes
        self.route_table_ext.add_router(self.ctx, self.router)

        subnet_bindings = [
            {'subnet_id': self.subnet_1_uuid,
             'routetable_id': self.subnet_route_table['id'],
             'cidr': '100.0.0.0/24'},
            {'subnet_id': self.subnet_2_uuid,
             'routetable_id': self.default_route_table['id'],
             'cidr': '200.0.0.0/24'}]
        # Associate subnet to subnet route table
        router = self.router
        router['subnet_route_table_bindings'] = subnet_bindings
        self._update_router(router)

        # Setup default route table and subnets associations
        ri = self.router_info
        dev0 = ri.get_internal_device_name(self._interfaces[0]['id'])
        expected_delete_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=200,
                      priority=100)]
        expected_add_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=201,
                      priority=100)]

        self.mock_delete_ip_rule.assert_has_calls(expected_delete_rules,
                                                  any_order=True)
        self.mock_add_ip_rule.assert_has_calls(expected_add_rules,
                                               any_order=True)

        # Remove subnet from router while subnet is associated to
        # subnet route table.
        router = self.router
        router['route_table_routes'] = (
            self.default_rt_default_routes +
            self.subnet_rt_default_routes +
            self.subnet_rt_custom_routes +
            self.gateway_rt_custom_routes)
        router['subnet_route_table_bindings'] = []
        self._update_router(router)

        # Delete default route table and subnets associations
        ri = self.router_info
        dev0 = ri.get_internal_device_name(self._interfaces[0]['id'])
        dev1 = ri.get_internal_device_name(self._interfaces[1]['id'])
        expected_delete_rules = [
            mock.call(namespace, '0.0.0.0/0', iif=dev0, table=201,
                      priority=100),
            mock.call(namespace, '0.0.0.0/0', iif=dev1, table=200,
                      priority=100)]

        self.mock_delete_ip_rule.assert_has_calls(expected_delete_rules,
                                                  any_order=True)

        # Delete system direct routes
        expected = [mock.call('100.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=200),
                    mock.call('200.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=200),
                    mock.call('100.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=201),
                    mock.call('200.0.0.0/24', proto='kernel', scope='link',
                              src='*********', table=201)]
        self.mock_ip_dev.route.delete_route.assert_has_calls(
            expected, any_order=True)
